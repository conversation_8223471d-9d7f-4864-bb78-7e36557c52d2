{"name": "s3-explorer", "type": "module", "module": "index.ts", "workspaces": ["packages/*"], "scripts": {"dev:client": "bun run --filter dccxx-s3-explorer dev", "dev:server": "bun run --filter server dev", "generate:codebase": "code2prompt . --output codebase.txt", "publish:client": "cd packages/client && npm run build && npm publish", "reformat": "prettier --write ./**/*.{ts,tsx}"}, "dependencies": {}, "devDependencies": {"@trivago/prettier-plugin-sort-imports": "^5.2.0", "@types/bun": "latest", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-packagejson": "^2.5.6", "prettier-plugin-sh": "^0.14.0", "prettier-plugin-sort-json": "^4.0.0"}, "peerDependencies": {"typescript": "^5.0.0"}}