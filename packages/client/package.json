{"name": "dccxx-s3-explorer", "version": "1.0.70", "description": "A production ready, batteries included starter template for Vite + React projects", "keywords": ["vite", "react", "boilerplate", "starter", "template"], "repository": {"type": "git", "url": "https://github.com/RicardoValdovinos/vite-react-boilerplate"}, "license": "MIT", "author": "<PERSON> <<EMAIL>>", "type": "module", "main": "./dist/s3-explorer.umd.cjs", "module": "./dist/s3-explorer.js", "files": ["dist", "README.md"], "scripts": {"build": "tsc && vite build", "commitizen": "exec < /dev/tty && git cz --hook || true", "commitlint": "commitlint --edit", "dev": "vite", "format": "prettier \"src/**/*.{ts,tsx,json}\" --write", "prepare": "husky", "preview": "vite preview", "publish": "npm publish", "setup": "git init && npx husky init && npx playwright install && shx rm .husky/pre-commit", "storybook": "storybook dev -p 6006", "storybook:build": "storybook build", "storybook:setup": "node node_modules/@storybook/addon-styling/bin/postinstall.js", "test": "vitest run src/ && playwright test", "test:e2e": "playwright test", "test:e2e:report": "playwright show-report", "test:unit": "vitest src/", "test:unit:coverage": "vitest --coverage src/"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@nivo/bar": "^0.87.0", "@nivo/core": "^0.87.0", "@nivo/line": "^0.87.0", "@nivo/pie": "^0.87.0", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.6", "@react-oauth/google": "^0.12.1", "@tanstack/react-query": "5.53.1", "@tanstack/react-router": "^1.51.6", "@tanstack/react-table": "^8.20.5", "ahooks": "^3.8.4", "axios": "^1.7.9", "axios-retry": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "i18next": "^23.14.0", "i18next-browser-languagedetector": "^8.0.0", "i18next-http-backend": "^2.6.1", "lodash": "^4.17.21", "lucide-react": "^0.468.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-hook-form": "^7.53.0", "react-i18next": "^15.0.1", "react-toastify": "^11.0.5", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8", "zustand": "^4.5.5"}, "devDependencies": {"@commitlint/cli": "^19.4.1", "@commitlint/config-conventional": "^19.4.1", "@faker-js/faker": "^8.4.1", "@headlessui/react": "^2.1.3", "@heroicons/react": "^2.2.0", "@hookform/devtools": "^4.3.1", "@playwright/test": "^1.46.1", "@storybook/addon-essentials": "^8.2.9", "@storybook/addon-interactions": "^8.2.9", "@storybook/addon-links": "^8.2.9", "@storybook/addon-themes": "^8.2.9", "@storybook/blocks": "^8.2.9", "@storybook/react": "^8.2.9", "@storybook/react-vite": "^8.2.9", "@storybook/test": "^8.2.9", "@tanstack/react-query-devtools": "5.53.1", "@tanstack/react-table-devtools": "^8.20.5", "@tanstack/router-devtools": "^1.51.6", "@tanstack/router-plugin": "^1.51.6", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@total-typescript/ts-reset": "^0.6.0", "@types/axios": "^0.14.4", "@types/lodash": "^4.17.16", "@types/node": "^22.5.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.7.0", "@vitest/coverage-v8": "2.0.5", "autoprefixer": "^10.4.20", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "globals": "^15.9.0", "husky": "^9.1.5", "jsdom": "^25.0.0", "postcss": "^8.4.41", "prettier": "^3.3.3", "prop-types": "^15.8.1", "shx": "^0.3.4", "storybook": "^8.2.9", "tailwindcss": "^3.4.10", "typescript": "^5.5.4", "vite": "5.4.2", "vite-plugin-dts": "^4.3.0", "vite-plugin-static-copy": "^1.0.6", "vitest": "2.0.5"}}