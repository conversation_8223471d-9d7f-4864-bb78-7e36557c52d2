/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as IndexImport } from './routes/index'
import { Route as SummaryIndexImport } from './routes/summary/index'
import { Route as JobIndexImport } from './routes/job/index'

// Create/Update Routes

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const SummaryIndexRoute = SummaryIndexImport.update({
  id: '/summary/',
  path: '/summary/',
  getParentRoute: () => rootRoute,
} as any)

const JobIndexRoute = JobIndexImport.update({
  id: '/job/',
  path: '/job/',
  getParentRoute: () => rootRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/job/': {
      id: '/job/'
      path: '/job'
      fullPath: '/job'
      preLoaderRoute: typeof JobIndexImport
      parentRoute: typeof rootRoute
    }
    '/summary/': {
      id: '/summary/'
      path: '/summary'
      fullPath: '/summary'
      preLoaderRoute: typeof SummaryIndexImport
      parentRoute: typeof rootRoute
    }
  }
}

// Create and export the route tree

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/job': typeof JobIndexRoute
  '/summary': typeof SummaryIndexRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/job': typeof JobIndexRoute
  '/summary': typeof SummaryIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/job/': typeof JobIndexRoute
  '/summary/': typeof SummaryIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/job' | '/summary'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/job' | '/summary'
  id: '__root__' | '/' | '/job/' | '/summary/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  JobIndexRoute: typeof JobIndexRoute
  SummaryIndexRoute: typeof SummaryIndexRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  JobIndexRoute: JobIndexRoute,
  SummaryIndexRoute: SummaryIndexRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.ts",
      "children": [
        "/",
        "/job/",
        "/summary/"
      ]
    },
    "/": {
      "filePath": "index.ts"
    },
    "/job/": {
      "filePath": "job/index.ts"
    },
    "/summary/": {
      "filePath": "summary/index.ts"
    }
  }
}
ROUTE_MANIFEST_END */
