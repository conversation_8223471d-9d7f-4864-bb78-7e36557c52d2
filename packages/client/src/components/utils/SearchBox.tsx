import { XMarkIcon } from '@heroicons/react/24/solid';
import { useDebounceEffect } from 'ahooks';
import { forwardRef, useImperativeHandle, useRef, useState } from 'react';

import { Input } from '@/components/ui/input.tsx';

const SearchBox = forwardRef<
  {
    setValue: (value: string) => void;
  },
  {
    onChange: (searchQuery: string) => void;
  }
>(({ onChange }, ref) => {
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [searchQuery, setSearchQuery] = useState('');

  useImperativeHandle(ref, () => ({
    setValue: (value: string) => {
      setSearchQuery(value);
      searchInputRef.current?.focus();
    },
  }));

  useDebounceEffect(
    () => {
      onChange?.(searchQuery);
    },
    [searchQuery],
    {
      wait: 300,
    },
  );

  return (
    <>
      <Input
        ref={searchInputRef}
        type="text"
        placeholder="Search files..."
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        className="pr-8"
      />
      {searchQuery && (
        <button
          onClick={() => {
            setSearchQuery('');
            searchInputRef.current?.focus();
          }}
          className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
        >
          <XMarkIcon className="h-5 w-5" />
        </button>
      )}
    </>
  );
});

export default SearchBox;
