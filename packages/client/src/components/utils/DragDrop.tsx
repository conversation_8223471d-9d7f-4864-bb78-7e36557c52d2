import React, { ReactNode, useEffect, useState } from 'react';
import { useDropzone } from 'react-dropzone';

interface FileUploadProps {
  onFilesSelected: (files: File[], typeUpload: 'folder' | 'files') => void;
  children?: ReactNode;
}

const FileUpload: React.FC<FileUploadProps> = ({ onFilesSelected, children }) => {
  const [isDragging, setIsDragging] = useState(false);

  const { getRootProps, getInputProps } = useDropzone({
    noClick: true,
    onDrop: (acceptedFiles: any) => {
      if (acceptedFiles && acceptedFiles.length > 0) {
        const isFolder = acceptedFiles[0].path.split('/').length > 2 ? true : false;

        if (isFolder) {
          const processedFiles = acceptedFiles.map(
            (file: File & { path?: string; relativePath?: string }) => {
              const pathValue = file.path || file.relativePath || '';
              const pathValueCustom = pathValue.split('/').slice(1).join('/');
              if (!file.webkitRelativePath || file.webkitRelativePath === '') {
                Object.defineProperty(file, 'webkitRelativePath', {
                  writable: true,
                  value: pathValueCustom,
                  enumerable: true,
                  configurable: true,
                });
              }
              return file;
            },
          );

          onFilesSelected(processedFiles, 'folder');
        } else {
          onFilesSelected(acceptedFiles, 'files');
        }
      }
      setIsDragging(false);
    },
    onDragOver: (e) => {
      e.preventDefault();
      if (!isDragging) {
        setIsDragging(true);
      }
    },
    multiple: true,
  });

  useEffect(() => {
    const handleDragLeave = (e: DragEvent) => {
      if (e.clientX === 0 && e.clientY === 0) {
        setIsDragging(false);
      }
    };
    document.addEventListener('dragleave', handleDragLeave);
    return () => {
      document.removeEventListener('dragleave', handleDragLeave);
    };
  }, []);

  return (
    <div {...getRootProps()} className="transition-opacity min-h-[600px]">
      {isDragging ? (
        <div className="flex flex-col items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50 h-screen"></div>
      ) : (
        children
      )}
      <input {...getInputProps()} />
    </div>
  );
};

export default FileUpload;
