import React, { useState } from 'react';

import {
  Tooltip as TooltipComponent,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';

interface TooltipSProps {
  className?: string;
  children: React.ReactNode;
  content: React.ReactNode;
  side?: 'left' | 'top' | 'right' | 'bottom' | undefined;
}

const TooltipS: React.FC<TooltipSProps> = ({ className, children, content, side }) => {
  const [open, setOpen] = useState(false);

  return (
    <TooltipProvider delayDuration={0}>
      <TooltipComponent open={open}>
        <TooltipTrigger asChild>
          <button
            type="button"
            className={cn('cursor-pointer', className)}
            onClick={() => setOpen(!open)}
            onMouseEnter={() => setOpen(true)}
            onMouseLeave={() => setOpen(false)}
            onTouchStart={() => setOpen(!open)}
            onKeyDown={(e) => {
              e.preventDefault();
              e.key === 'Enter' && setOpen(!open);
            }}
          >
            {children}
          </button>
        </TooltipTrigger>
        <TooltipContent side={side} className={!content ? 'hidden' : ''}>
          <span className="inline-block">{content}</span>
        </TooltipContent>
      </TooltipComponent>
    </TooltipProvider>
  );
};

export default TooltipS;
