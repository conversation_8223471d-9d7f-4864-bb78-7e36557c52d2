import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from '@/components/ui/toast';
import { useToast } from '@/hooks/use-toast';

export function Toaster() {
  const { toasts } = useToast();

  return (
    <ToastProvider>
      {toasts.map(function ({ id, title, description, action, ...props }) {
        return (
          <Toast key={id} {...props} className={`s3-explr-ui-toast ${props.className || ''}`}>
            <div className="s3-explr-ui-toast-content grid gap-1">
              {title && <ToastTitle className="s3-explr-ui-toast-title">{title}</ToastTitle>}
              {description && (
                <ToastDescription className="s3-explr-ui-toast-description">
                  {description}
                </ToastDescription>
              )}
            </div>
            {action && <div className="s3-explr-ui-toast-action">{action}</div>}
            <ToastClose className="s3-explr-ui-toast-close" />
          </Toast>
        );
      })}
      <ToastViewport className="s3-explr-ui-toast-viewport" />
    </ToastProvider>
  );
}
