import { addWithAudit, createAuditOptions, getBeforeUpdate, removeWithAudit, updateWithAudit } from '../modules/audit-wrapper';
import { getAuditLogs, logCreate, logDelete, logRead, logUpdate } from '../modules/audit-logger';

export class AuditIntegrationExamples {
  
  static async createKnowledgeBaseWithAudit(
    knowledgeBaseId: string,
    dataSourceId: string,
    folderName: string,
    indexName: string,
    username: string,
    userId: string,
    req?: any,
  ) {
    const insertQuery = `
      INSERT INTO knowledge_base (knowledge_base_id, data_source_ids, folder_name, index_name)
      VALUES ($1, $2, $3, $4) RETURNING *
    `;
    const values = [knowledgeBaseId, dataSourceId, folderName, indexName];
    
    const data = {
      knowledge_base_id: knowledgeBaseId,
      data_source_ids: dataSourceId,
      folder_name: folderName,
      index_name: indexName,
    };
    
    const auditOptions = createAuditOptions(username, userId, req, {
      operation: 'create_knowledge_base',
      folder_name: folderName,
    });
    
    return await addWithAudit(
      insertQuery,
      values,
      'knowledge_base',
      folderName,
      data,
      auditOptions,
    );
  }

  static async updateTextSummaryWithAudit(
    id: string,
    textSummary: string,
    username: string,
    userId: string,
    req?: any,
  ) {
    const beforeData = await getBeforeUpdate('text_summary', 'id', id);
    
    if (!beforeData) {
      throw new Error('Text summary not found');
    }
    
    const query = 'UPDATE text_summary SET text_summary = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 RETURNING *';
    const values = [textSummary, id];
    
    const afterData = {
      ...beforeData,
      text_summary: textSummary,
      updated_at: new Date(),
    };
    
    const auditOptions = createAuditOptions(username, userId, req, {
      operation: 'update_text_summary',
      summary_id: id,
    });
    
    return await updateWithAudit(
      query,
      values,
      'text_summary',
      id,
      beforeData.file_name || `Summary ${id}`,
      beforeData,
      afterData,
      auditOptions,
    );
  }

  static async deleteKnowledgeBaseWithAudit(
    knowledgeBaseId: string,
    username: string,
    userId: string,
    req?: any,
  ) {
    const beforeData = await getBeforeUpdate('knowledge_base', 'knowledge_base_id', knowledgeBaseId);
    
    if (!beforeData) {
      throw new Error('Knowledge base not found');
    }
    
    const query = 'DELETE FROM knowledge_base WHERE knowledge_base_id = $1';
    const values = [knowledgeBaseId];
    
    const auditOptions = createAuditOptions(username, userId, req, {
      operation: 'delete_knowledge_base',
      knowledge_base_id: knowledgeBaseId,
    });
    
    return await removeWithAudit(
      query,
      values,
      'knowledge_base',
      knowledgeBaseId,
      beforeData.folder_name || `KB ${knowledgeBaseId}`,
      beforeData,
      auditOptions,
    );
  }

  static async createUserTokenWithAudit(
    userId: string,
    refreshToken: string,
    tokenType: string,
    username: string,
    req?: any,
  ) {
    const insertQuery = 'INSERT INTO user_tokens (user_id, refresh_token, type) VALUES ($1, $2, $3) RETURNING *';
    const values = [userId, refreshToken, tokenType];
    
    const data = {
      user_id: userId,
      refresh_token: refreshToken,
      type: tokenType,
    };
    
    const auditOptions = createAuditOptions(username, userId, req, {
      operation: 'create_user_token',
      token_type: tokenType,
    });
    
    return await addWithAudit(
      insertQuery,
      values,
      'user_token',
      `${tokenType} Token for ${username}`,
      data,
      auditOptions,
    );
  }

  static async logFileReadAccess(
    fileId: string,
    fileName: string,
    username: string,
    userId: string,
    req?: any,
    searchParams?: Record<string, any>,
  ) {
    const auditOptions = createAuditOptions(username, userId, req, {
      operation: 'read_file',
      file_name: fileName,
    });
    
    await logRead(
      'file',
      fileId,
      fileName,
      auditOptions,
      searchParams,
    );
  }

  static async logFolderCreation(
    folderPath: string,
    username: string,
    userId: string,
    req?: any,
  ) {
    const data = {
      folder_path: folderPath,
      created_at: new Date(),
    };
    
    const auditOptions = createAuditOptions(username, userId, req, {
      operation: 'create_folder',
      folder_path: folderPath,
    });
    
    await logCreate(
      'folder',
      folderPath,
      folderPath.split('/').pop() || folderPath,
      data,
      auditOptions,
    );
  }

  static async getAuditHistory(
    target_type?: string,
    target_id?: string,
    username?: string,
    start_date?: Date,
    end_date?: Date,
    page = 1,
    limit = 50,
  ) {
    const offset = (page - 1) * limit;
    
    return await getAuditLogs({
      target_type: target_type as any,
      target_id,
      username,
      start_date,
      end_date,
      limit,
      offset,
    });
  }

  static async getUserActivity(
    username: string,
    days = 30,
    limit = 100,
  ) {
    const start_date = new Date();
    start_date.setDate(start_date.getDate() - days);
    
    return await getAuditLogs({
      username,
      start_date,
      limit,
      offset: 0,
    });
  }

  static async getFailedOperations(
    start_date?: Date,
    end_date?: Date,
    limit = 50,
  ) {
    return await getAuditLogs({
      success: false,
      start_date,
      end_date,
      limit,
      offset: 0,
    });
  }

  static async getTargetHistory(
    target_type: string,
    target_id: string,
    limit = 20,
  ) {
    return await getAuditLogs({
      target_type: target_type as any,
      target_id,
      limit,
      offset: 0,
    });
  }
}

export const {
  createKnowledgeBaseWithAudit,
  updateTextSummaryWithAudit,
  deleteKnowledgeBaseWithAudit,
  createUserTokenWithAudit,
  logFileReadAccess,
  logFolderCreation,
  getAuditHistory,
  getUserActivity,
  getFailedOperations,
  getTargetHistory,
} = AuditIntegrationExamples;
