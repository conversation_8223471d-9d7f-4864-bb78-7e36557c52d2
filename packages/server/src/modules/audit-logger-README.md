# Audit Logger Module

<PERSON>ệ thống logging CRUD operations cho việc theo dõi và kiểm toán các hoạt động trong database.

## C<PERSON>u trúc Table

Module này giả định bạn đã có table `audit_logs` với cấu trúc sau:

```sql
CREATE TABLE audit_logs (
    id SERIAL PRIMARY KEY,
    action VARCHAR(10) NOT NULL CHECK (action IN ('CREATE', 'READ', 'UPDATE', 'DELETE')),
    target_type VARCHAR(20) NOT NULL,
    target_id VARCHAR(255),
    target_name VARCHAR(500),
    username VARCHAR(255) NOT NULL,
    user_id VARCHAR(255),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    changes JSONB,
    metadata JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    error_message TEXT,
    success BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

## Cách sử dụng cơ bản

### 1. Import các functions cần thiết

```typescript
import { logCreate, logRead, logUpdate, logDelete, getAuditLogs } from './modules/audit-logger.ts';
import { addWithAudit, updateWithAudit, removeWithAudit, createAuditOptions } from './modules/audit-wrapper.ts';
```

### 2. Logging CREATE operations

```typescript
// Cách 1: Sử dụng direct logging
await logCreate(
  'knowledge_base',
  knowledgeBaseId,
  folderName,
  {
    knowledge_base_id: knowledgeBaseId,
    data_source_ids: dataSourceId,
    folder_name: folderName,
    index_name: indexName,
  },
  {
    username: 'admin',
    user_id: 'user123',
    ip_address: '***********',
    metadata: { operation: 'create_kb' }
  }
);

// Cách 2: Sử dụng wrapper (tự động log)
const result = await addWithAudit(
  insertQuery,
  values,
  'knowledge_base',
  folderName,
  data,
  auditOptions
);
```

### 3. Logging READ operations

```typescript
await logRead(
  'file',
  fileId,
  fileName,
  {
    username: 'user1',
    user_id: 'user123',
  },
  { search_query: 'document.pdf' }
);
```

### 4. Logging UPDATE operations

```typescript
// Lấy dữ liệu trước khi update
const beforeData = await getBeforeUpdate('text_summary', 'id', summaryId);

// Thực hiện update với audit
const result = await updateWithAudit(
  updateQuery,
  values,
  'text_summary',
  summaryId,
  'Summary Document',
  beforeData,
  afterData,
  auditOptions
);
```

### 5. Logging DELETE operations

```typescript
// Lấy dữ liệu trước khi xóa
const beforeData = await getBeforeUpdate('knowledge_base', 'knowledge_base_id', kbId);

// Xóa với audit
const result = await removeWithAudit(
  deleteQuery,
  values,
  'knowledge_base',
  kbId,
  beforeData.folder_name,
  beforeData,
  auditOptions
);
```

## Tạo Audit Options

```typescript
// Từ HTTP request
const auditOptions = createAuditOptions(
  'username',
  'user_id',
  req, // HTTP request object
  { operation: 'custom_operation' }
);

// Manual
const auditOptions = {
  username: 'admin',
  user_id: 'user123',
  ip_address: '***********',
  user_agent: 'Mozilla/5.0...',
  session_id: 'session123',
  metadata: { custom_field: 'value' }
};
```

## Truy vấn Audit Logs

### Lấy logs với filter

```typescript
const result = await getAuditLogs({
  action: 'UPDATE',
  target_type: 'file',
  username: 'admin',
  start_date: new Date('2024-01-01'),
  end_date: new Date('2024-12-31'),
  success: true,
  limit: 50,
  offset: 0
});

console.log(result.logs);
console.log(`Total: ${result.total_count}`);
```

### Lấy hoạt động của user

```typescript
const userActivity = await getAuditLogs({
  username: 'admin',
  start_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
  limit: 100
});
```

### Lấy lịch sử của một object

```typescript
const objectHistory = await getAuditLogs({
  target_type: 'knowledge_base',
  target_id: 'kb123',
  limit: 20
});
```

## API Endpoints

Thêm vào main app:

```typescript
import auditRoutes from './routes/audit-routes.ts';

app.route('/api', auditRoutes);
```

### Available endpoints:

- `GET /api/audit-logs` - Lấy audit logs với filters
- `GET /api/audit-logs/user/:username` - Lấy hoạt động của user
- `GET /api/audit-logs/target/:target_type/:target_id` - Lấy lịch sử của object
- `GET /api/audit-logs/failed` - Lấy các operations thất bại
- `GET /api/audit-logs/stats` - Thống kê audit logs

## Target Types được hỗ trợ

- `agent`
- `folder`
- `file`
- `document`
- `knowledge_base`
- `user_token`
- `text_summary`
- `job`

## Error Handling

Module tự động handle errors và log chúng vào audit table:

```typescript
// Nếu operation thất bại, sẽ tự động log với success: false
try {
  await someOperation();
} catch (error) {
  // Error sẽ được log tự động với error_message
}
```

## Best Practices

1. **Luôn cung cấp username**: Đây là field bắt buộc
2. **Sử dụng meaningful target_name**: Giúp dễ đọc trong logs
3. **Thêm metadata hữu ích**: Context thêm cho operations
4. **Sử dụng wrapper functions**: Tự động handle logging
5. **Regular cleanup**: Xóa old logs để tránh table quá lớn

## Performance Considerations

- Table có indexes trên các fields thường query
- Sử dụng pagination khi query large datasets
- Consider archiving old logs
- Monitor table size và performance
