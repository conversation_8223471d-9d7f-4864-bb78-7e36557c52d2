import {
  <PERSON>rockAgent,
  DeleteKnowledgeBaseCommand,
  ListIngestionJobsCommand,
  StartIngestionJobCommand,
} from '@aws-sdk/client-bedrock-agent';
import {
  CopyObjectCommand,
  DeleteObjectsCommand,
  GetObjectCommand,
  ListObjectsV2Command,
  type ListObjectsV2CommandOutput,
  PutObjectCommand,
  S3Client,
  type _Object,
} from '@aws-sdk/client-s3';
import { NodeHttpHandler } from '@smithy/node-http-handler';
import { schedules, tasks } from '@trigger.dev/sdk/v3';
import archiver from 'archiver';
import { map } from 'lodash';
import { PassThrough, Readable } from 'stream';
import { v4 as uuidv4 } from 'uuid';

import type { aiMetadata } from '../trigger/crawl/ai-metadata.ts';
import { syncS3 } from '../trigger/cron/sync-s3.ts';
import {
  addDocuments,
  deleteDocumentsByField,
  indexSync,
  initIndexSync,
  swapIndexes,
  updateDocument,
  updateDocumentByKey,
  updateDocumentByName,
} from './meilisearch.ts';
import { add, get, remove, update } from './postgres';
import redis from './redis.ts';

const client = new BedrockAgent({
  region: process.env.S3_STORAGE_REGION || 'us-east-1',
  credentials: {
    accessKeyId:
      process.env.BEDROCK_ACCESS_KEY_ID! ||
      process.env.S3_STORAGE_ACCESS_KEY_ID! ||
      process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey:
      process.env.BEDROCK_SECRET_ACCESS_KEY! ||
      process.env.S3_STORAGE_SECRET_ACCESS_KEY! ||
      process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

export const s3Client = new S3Client({
  credentials: {
    accessKeyId:
      process.env.BEDROCK_ACCESS_KEY_ID! ||
      process.env.S3_STORAGE_ACCESS_KEY_ID! ||
      process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey:
      process.env.BEDROCK_SECRET_ACCESS_KEY! ||
      process.env.S3_STORAGE_SECRET_ACCESS_KEY! ||
      process.env.AWS_SECRET_ACCESS_KEY!,
  },
  region: process.env.S3_STORAGE_REGION || 'us-east-1',
  requestHandler: new NodeHttpHandler({
    connectionTimeout: 600000,
  }),
});
export const BUCKET_NAME = process.env.S3_STORAGE_BUCKET_NAME || 'unknown';

export const bedrockAgent = new BedrockAgent({
  region: process.env.S3_STORAGE_REGION || 'us-east-1',
  credentials: {
    accessKeyId:
      process.env.BEDROCK_ACCESS_KEY_ID! ||
      process.env.S3_STORAGE_ACCESS_KEY_ID! ||
      process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey:
      process.env.BEDROCK_SECRET_ACCESS_KEY! ||
      process.env.S3_STORAGE_SECRET_ACCESS_KEY! ||
      process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

export type S3Object = {
  key: string;
  name: string;
  size: number;
  lastModified: Date | string;
  contentType: string;
  isFolder: boolean;
  etag?: string;
  visibility?: 'private' | 'public';
  allowedGroups?: string[];
  allowedChatFlows?: string[];
};

export async function uploadToS3(
  fileName: string,
  fileBuffer: Buffer,
  metadata: object,
): Promise<{ pdfUrl: string; metadataUrl: string }> {
  try {
    console.log(`Starting upload process for: ${fileName}`);

    const s3Key = `pdfs/${Date.now()}-${fileName}`;
    const metadataKey = `${s3Key}.metadata.json`;

    console.log(`Generated S3 keys - PDF: ${s3Key}, Metadata: ${metadataKey}`);

    // Upload PDF
    const uploadPDFCommand = new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: s3Key,
      Body: fileBuffer,
      ContentType: 'application/pdf',
    });

    console.log(`Uploading PDF to S3: ${s3Key}`);

    // Upload Metadata
    const uploadMetadataCommand = new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: metadataKey,
      Body: JSON.stringify(metadata, null, 2),
      ContentType: 'application/json',
    });

    console.log(`Uploading Metadata to S3: ${metadataKey}`);

    await Promise.all([
      s3Client
        .send(uploadPDFCommand)
        .then(() => console.log(`PDF uploaded successfully: ${s3Key}`)),
      s3Client
        .send(uploadMetadataCommand)
        .then(() => console.log(`Metadata uploaded successfully: ${metadataKey}`)),
    ]);

    console.log(`Upload process completed for: ${fileName}`);

    return {
      pdfUrl: `https://${BUCKET_NAME}.s3.amazonaws.com/${s3Key}`,
      metadataUrl: `https://${BUCKET_NAME}.s3.amazonaws.com/${metadataKey}`,
    };
  } catch (error) {
    console.error(`Error uploading PDF & Metadata to S3:`, error);
    throw new Error(`Failed to upload PDF & Metadata '${fileName}' to S3: ${error}`);
  }
}

export const formatFilesByChatFlows = (files: any) => {
  const filtered = files.filter((file: any) => Array.isArray(file.allowedChatFlows));
  const grouped: any = {};

  filtered.forEach((item: any) => {
    item.scope =
      !item.visibility || item.visibility === 'public'
        ? 'Chung'
        : Array.isArray(item.allowedGroups)
          ? 'Nhom'
          : 'Ca nhan';
    item.allowedChatFlows.forEach((flow: any) => {
      if (!grouped[flow]) {
        grouped[flow] = [];
      }
      grouped[flow].push(item);
    });
  });

  return grouped;
};

export const getAllFilesWithCache = async (
  prefix: string,
  cache: boolean = false,
  displayPrefixes: string = '',
  excludePrivateFiles: boolean = false,
): Promise<Record<string, S3Object[]>> => {
  const cacheKey = `s3:files:${BUCKET_NAME}:${prefix}`;
  const displayPrefixesArr = displayPrefixes ? JSON?.parse(displayPrefixes) : [];
  const safeJsonParse = (data: string) => {
    try {
      return JSON.parse(data);
    } catch (error) {
      console.error('❌ JSON parse error:', error);
      return {};
    }
  };

  const processCache = (data: string): Record<string, S3Object[]> => {
    const parsed = safeJsonParse(data);
    Object.values(parsed)
      .flat()
      .forEach((obj: any) => {
        if (obj.lastModified) {
          obj.lastModified = new Date(obj.lastModified);
        }
      });
    return parsed;
  };

  const getFilteredData = (parsed: Record<string, S3Object[]>): Record<string, S3Object[]> => {
    if (!parsed) return {};

    const filteredData = Object.fromEntries(
      Object.entries(parsed).filter(([key]) =>
        displayPrefixesArr.some((p: string) => key.startsWith(p)),
      ),
    );

    if (parsed['']) {
      const rootFolder = parsed[''].filter((item: any) => displayPrefixesArr.includes(item.key));
      if (rootFolder.length > 0) {
        filteredData[''] = rootFolder;
      }
    }
    return filteredData;
  };

  const filterPublicFiles = (parsed: Record<string, S3Object[]>): Record<string, S3Object[]> => {
    if (!parsed) return {};

    return Object.fromEntries(
      Object.entries(parsed).map(([k, v]) => [
        k,
        v.filter((item) => {
          return ![`${prefix}groups/`, `${prefix}users/`].includes(item.key);
        }),
      ]),
    );
  };

  try {
    let cachedData = null;

    if (cache) {
      cachedData = await redis.get(cacheKey);
    }

    if (cachedData) {
      let parsedCache = processCache(cachedData);

      if (excludePrivateFiles) {
        parsedCache = filterPublicFiles(parsedCache);
      }

      if (displayPrefixesArr.length > 0) {
        return getFilteredData(parsedCache);
      } else {
        return parsedCache;
      }
    }

    let files = await getAllFiles(prefix);

    await redis.set(cacheKey, JSON.stringify(files), 'EX', 604800);

    if (excludePrivateFiles) {
      files = filterPublicFiles(files);
    }

    if (displayPrefixesArr.length > 0) {
      return getFilteredData(files);
    }

    return files;
  } catch (error) {
    console.warn('[1]', error);
    return getAllFiles(prefix);
  }
};

export const getAllFiles = async (prefix: string): Promise<Record<string, S3Object[]>> => {
  const result: Record<string, S3Object[]> = {};
  const delimiter = '/';

  const command = new ListObjectsV2Command({
    Bucket: BUCKET_NAME,
    Prefix: prefix,
    Delimiter: delimiter,
  });

  try {
    const response = await s3Client.send(command);

    const currentPrefix = prefix || '';
    result[currentPrefix] = [];

    if (response.CommonPrefixes) {
      for (const commonPrefix of response.CommonPrefixes) {
        if (commonPrefix.Prefix) {
          const name = commonPrefix.Prefix.split('/').slice(-2)[0];
          result[currentPrefix].push({
            key: commonPrefix.Prefix,
            name,
            size: 0,
            lastModified: '-',
            contentType: 'application/x-directory',
            isFolder: true,
          });
        }
      }
    }

    if (response.Contents) {
      for (const content of response.Contents) {
        if (
          content.Key === prefix ||
          content.Key?.endsWith('/') ||
          content.Key?.endsWith('.metadata.json')
        )
          continue;

        const name = content.Key!.split('/').pop() || '';
        result[currentPrefix].push({
          key: content.Key!,
          name,
          size: content.Size || 0,
          lastModified: content.LastModified || new Date(),
          contentType: getContentType(name),
          isFolder: false,
          etag: content.ETag,
        });
      }
    }

    console.log(`Completed processing for prefix: ${prefix}`);
    return result;
  } catch (error) {
    console.error(`Error listing objects for prefix ${prefix}:`, error);

    throw error;
  } finally {
    // if (isRoot) {
    //   console.log('[ALL DONE]');
    // }
  }
};

export const createFolder = async (prefix: string): Promise<void> => {
  // Ensure the prefix ends with a forward slash
  const folderKey = prefix.endsWith('/') ? prefix : `${prefix}/`;

  const command = new PutObjectCommand({
    Bucket: BUCKET_NAME,
    Key: folderKey,
    ContentLength: 0,
  });

  try {
    await s3Client.send(command);
  } catch (error) {
    console.error('Error creating folder in S3:', error);
    throw new Error(`Failed to create folder '${folderKey}' in S3: ${error}`);
  }
};

export const deleteObjects = async (
  paths: string[],
  isRemoveMeiLiDb?: boolean,
  isRename?: boolean,
): Promise<void> => {
  for (const path of paths) {
    if (path.endsWith('/')) {
      // Handle directory deletion

      const parts = path.split('/').filter(Boolean);
      console.log('🚀 ~ s3.ts:297 ~ deleteObjects ~ parts:', parts);
      const folderName = parts[parts.length - 1];
      console.log('🚀 ~ s3.ts:298 ~ deleteObjects ~ folderName:', folderName);

      if (!isRename) {
        const folderPrefix = path.replace(new RegExp(`${folderName}/?$`), '');

        await remove(`DELETE FROM onedrive_synced_items WHERE prefix = $1 AND file_name = $2`, [
          folderPrefix,
          folderName,
        ]);

        await remove(`DELETE FROM onedrive_synced_items WHERE prefix LIKE $1`, [`${path}%`]);

        await remove(`DELETE FROM sharepoint_synced_items WHERE prefix = $1 AND file_name = $2`, [
          folderPrefix,
          folderName,
        ]);

        await remove(`DELETE FROM sharepoint_synced_items WHERE prefix LIKE $1`, [`${path}%`]);
      }

      if (parts.length === 1) {
        const record = await get('SELECT * FROM knowledge_base WHERE folder_name = $1', [
          folderName + '/',
        ]);
        if (record.length > 0) {
          const deleteCommand = new DeleteKnowledgeBaseCommand({
            knowledgeBaseId: record[0]?.knowledge_base_id,
          });
          await client.send(deleteCommand);
          await remove('DELETE FROM knowledge_base WHERE knowledge_base_id = $1', [
            record[0]?.knowledge_base_id,
          ]);
        }
      }

      const command = new ListObjectsV2Command({
        Bucket: BUCKET_NAME,
        Prefix: path,
      });

      try {
        const response = await s3Client.send(command);
        if (!response.Contents || response.Contents.length === 0) {
          continue;
        }

        // Collect all objects to delete, including metadata files
        const objectsToDelete = response.Contents.map((obj) => ({ Key: obj.Key }));

        // S3 requires objects to be deleted in batches of 1000 or fewer
        const chunkSize = 500;
        for (let i = 0; i < objectsToDelete.length; i += chunkSize) {
          const chunk = objectsToDelete.slice(i, i + chunkSize);
          const deleteCommand = new DeleteObjectsCommand({
            Bucket: BUCKET_NAME,
            Delete: {
              Objects: chunk,
              Quiet: true,
            },
          });

          await s3Client.send(deleteCommand);
        }

        if (isRemoveMeiLiDb) {
          await deleteDocumentsByField(path);
        }
      } catch (error) {
        console.error(`Error deleting directory '${path}':`, error);
        throw new Error(`Failed to delete directory '${path}': ${error}`);
      }
    } else {
      // Handle single file deletion
      try {
        // Delete both the file and its potential metadata file
        const objectsToDelete = [{ Key: path }, { Key: `${path}.metadata.json` }];

        if (!isRename) {
          await remove('DELETE FROM onedrive_synced_items WHERE prefix LIKE $1', [`${paths}%`]);
          await remove('DELETE FROM sharepoint_synced_items WHERE prefix LIKE $1', [`${paths}%`]);
        }

        const deleteCommand = new DeleteObjectsCommand({
          Bucket: BUCKET_NAME,
          Delete: {
            Objects: objectsToDelete,
            Quiet: true,
          },
        });
        await s3Client.send(deleteCommand);
        if (isRemoveMeiLiDb) {
          await deleteDocumentsByField(path);
        }
      } catch (error) {
        console.error(`Error deleting file '${path}':`, error);
        throw new Error(`Failed to delete file '${path}': ${error}`);
      }
    }
  }
};

export const checkFilesExisted = async (
  prefix: string,
  files: Array<{ name: string }>,
  folderName?: string,
) => {
  const command = new ListObjectsV2Command({
    Bucket: BUCKET_NAME,
    Prefix: prefix,
    Delimiter: '/',
  });
  const response = await s3Client.send(command);

  if (folderName && response.CommonPrefixes?.some((p) => p.Prefix === `${prefix}${folderName}/`)) {
    return {
      status: 400,
      message: `Folder '${folderName}' đã tồn tại trong thư mục '${prefix || '/'}'.`,
      isFolder: true,
      name: folderName,
    };
  }

  const existingFiles = response.Contents?.map((content) => content.Key) || [];
  const duplicateFiles = files.filter((file) => existingFiles.includes(`${prefix}${file.name}`));
  if (duplicateFiles.length > 0) {
    return {
      status: 400,
      message: `Các tệp sau đã tồn tại trong thư mục '${prefix}': ${duplicateFiles.map((file) => file.name).join(', ')}.`,
      isFolder: false,
      names: duplicateFiles.map((file) => file.name),
    };
  }

  return {
    status: 200,
    message: `Không có tệp nào trùng lặp trong thư mục '${prefix}'.`,
  };
};

export const uploadMultipleFiles = async (
  files: Array<{
    prefix: string;
    name: string;
    file: Buffer;
    contentType?: string;
  }>,
  metadata?: {
    creator?: string;
    creatorId?: string;
    DataSource?: string;
  },
  documentData?: {
    chatFlowId?: string;
    userId?: string;
    groupId?: string;
  },
): Promise<string[]> => {
  const getBufferSize = (buffer: Buffer): number => {
    return buffer.byteLength;
  };

  const uploadPromises = files.flatMap(async ({ prefix, name, file, contentType }) => {
    const normalizedPrefix = prefix ? (prefix.endsWith('/') ? prefix : `${prefix}/`) : '';
    const key = `${normalizedPrefix}${name}`;

    const fileUploadCommand = new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
      Body: file,
      ContentType: contentType || getContentType(name),
    });

    try {
      // Only create metadata file if the file itself is not a metadata file
      if (!key.endsWith('.metadata.json')) {
        // Create metadata from prefix parts
        const prefixParts = normalizedPrefix.split('/').filter(Boolean);
        const newDate = new Date();
        const metadataAttributes: Record<string, string> = {
          category: prefixParts[0] || ' ',
          fileName: name,
          createAt: newDate.toISOString(),
          creator: metadata?.creator || ' ',
          creatorId: metadata?.creatorId || ' ',
          AccessLevel: ' ',
          KeywordAISuggestion: ' ',
          DataSource: metadata?.DataSource || 'Tệp tải lên',
          SuggestedAICategories: ' ',
          SummaryOfSuggestedAI: ' ',
          path: key,
          version: '1.0',
          availabilityDate: new Date(newDate.getTime() + 365 * 24 * 60 * 60 * 1000).toISOString(), // Default 1 year from now
        };

        // Add dynamic sub-category keys starting from index 1
        for (let i = 1; i < prefixParts.length; i++) {
          metadataAttributes[`sub_cate_${i}`] = prefixParts[i] || '';
        }

        const metadataContent = {
          metadataAttributes,
        };

        const metadataUploadCommand = new PutObjectCommand({
          Bucket: BUCKET_NAME,
          Key: `${key}.metadata.json`,
          Body: JSON.stringify(metadataContent, null, 2),
          ContentType: 'application/json',
        });

        const [fileUploaded] = await Promise.all([
          s3Client.send(fileUploadCommand),
          s3Client.send(metadataUploadCommand),
        ]);

        const visibility =
          documentData?.chatFlowId || documentData?.userId || documentData?.groupId
            ? 'private'
            : 'public';

        const document = {
          id: uuidv4(),
          name: key?.split('/')?.pop() || key,
          key: key,
          size: getBufferSize(file) || 0,
          description: '',
          userId: metadata?.creatorId || '0299b2b0-980a-4d5c-8692-bc69e3b576e2',
          idFolder: false,
          etag: fileUploaded?.ETag || '',
          lastModified: newDate.toISOString(),
          AccessLevel: '',
          KeywordAISuggestion: '',
          DataSource: '',
          SuggestedAICategories: '',
          SummaryOfSuggestedAI: '',
          visibility,
          allowedChatFlows: documentData?.chatFlowId ? [documentData?.chatFlowId] : '',
          allowedUserIds: documentData?.userId ? [documentData?.userId] : '',
          allowedGroups: documentData?.groupId ? [documentData?.groupId] : '',
        };
        await addDocuments([document]);
      } else {
        // If it's a metadata file, just upload the file without creating additional metadata
        await s3Client.send(fileUploadCommand);
      }
      return key;
    } catch (error) {
      console.error(`Error uploading file '${key}' or its metadata to S3:`, error);
      throw new Error(`Failed to upload file '${key}' or its metadata to S3: ${error}`);
    }
  });

  const folderRoot = files[0].prefix.split('/')[0];

  if (folderRoot) {
    await markIngestionJobAsInStock(folderRoot);
  }

  return await Promise.all(uploadPromises);
};

export const updateMultipleFiles = async (files: S3Object[]) => {
  try {
    const response = await Promise.allSettled(
      files.map(async (file) => await updateDocument(file.key, file)),
    );

    return response;
  } catch (error) {
    console.error('Error updating files to s3', error);
    throw new Error(`Error updating files to S3: ${error}`);
  }
};

export const uploadFile = async (
  prefix: string,
  name: string,
  file: Buffer,
  contentType?: string,
): Promise<void> => {
  try {
    await uploadMultipleFiles([{ prefix, name, file, contentType }]);
  } catch (error) {
    console.error(`Error uploading file '${name}' to S3:`, error);
    throw new Error(`Failed to upload file '${name}' to S3: ${error}`);
  }
};

export async function processCompletedJob(jobData: any, prefix: string, crawlId: string) {
  // Store data in Redis with a unique key for each item
  const dataIds = await Promise.all(
    map(jobData.data, async (item) => {
      const id = uuidv4();
      const key = `crawl:data:${id}`;

      await redis.set(
        key,
        JSON.stringify({
          html: item.html,
          markdown: item.markdown,
          url: item.metadata.url,
          prefix,
          title: item.metadata.title,
          crawlId,
        }),
        'EX',
        60 * 60,
      ); // Expire in 1 hour

      return id;
    }),
  );

  // Process IDs in batches of 20
  const batchSize = +(process.env.PROCESS_BATCH_SIZE || 20);

  for (let i = 0; i < dataIds.length; i += batchSize) {
    const batch = dataIds.slice(i, i + batchSize);
    await tasks.batchTriggerAndWait<typeof aiMetadata>(
      'crawl/ai-metadata',
      map(batch, (id) => ({
        payload: { dataId: id },
      })),
    );
  }
}

export function getContentType(filename: string): string {
  const extension = filename.split('.').pop()?.toLowerCase();
  const contentTypes: Record<string, string> = {
    txt: 'text/plain',
    pdf: 'application/pdf',
    png: 'image/png',
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    json: 'application/json',
  };

  return contentTypes[extension || ''] || 'application/octet-stream';
}

function isTextFile(path: string, contentType: string): boolean {
  return (
    contentType.startsWith('text/') ||
    contentType === 'application/json' ||
    path.endsWith('.md') ||
    path.endsWith('.html') ||
    path.endsWith('.txt')
  );
}

export const renameObject = async (
  oldPath: string,
  newPath: string,
  isRemoveMeiLiDb?: boolean,
): Promise<void> => {
  if (oldPath.endsWith('/')) {
    if (oldPath === newPath) {
      return;
    }
    // Handle directory rename
    const command = new ListObjectsV2Command({
      Bucket: BUCKET_NAME,
      Prefix: oldPath,
    });

    try {
      const response = await s3Client.send(command);
      if (!response.Contents || response.Contents.length === 0) {
        return;
      }

      // Copy all objects with the new prefix
      for (const content of response.Contents) {
        if (!content.Key) continue;

        const newKey = content.Key.replace(oldPath, newPath);
        const copyCommand = new CopyObjectCommand({
          Bucket: BUCKET_NAME,
          CopySource: encodeURIComponent(`${BUCKET_NAME}/${content.Key}`),
          Key: newKey,
        });

        await s3Client.send(copyCommand);
      }

      // Delete old objects
      await deleteObjects([oldPath], isRemoveMeiLiDb, true);

      await updateDocumentByName(oldPath, newPath, 'key');
    } catch (error) {
      console.error(`Error renaming directory from '${oldPath}' to '${newPath}':`, error);
      throw new Error(`Failed to rename directory from '${oldPath}' to '${newPath}': ${error}`);
    }
  } else {
    // Handle single file rename
    try {
      const copyCommand = new CopyObjectCommand({
        Bucket: BUCKET_NAME,
        CopySource: encodeURIComponent(`${BUCKET_NAME}/${oldPath}`),
        Key: newPath,
      });

      const copyCommandMetadata = new CopyObjectCommand({
        Bucket: BUCKET_NAME,
        CopySource: encodeURIComponent(`${BUCKET_NAME}/${oldPath + '.metadata.json'}`),
        Key: newPath + '.metadata.json',
      });

      await s3Client.send(copyCommandMetadata);
      await s3Client.send(copyCommand);

      // Delete old file
      await deleteObjects([oldPath], isRemoveMeiLiDb, true);

      await updateDocumentByName(oldPath, newPath, 'name');
    } catch (error) {
      console.error(`Error renaming file from '${oldPath}' to '${newPath}':`, error);
      throw new Error(`Failed to rename file from '${oldPath}' to '${newPath}': ${error}`);
    }
  }
};

export type DownloadResult = {
  stream: Readable | PassThrough;
  filename: string;
  contentType: string;
  contentLength?: number;
};

export const downloadObjects = async (paths: string[]): Promise<DownloadResult> => {
  // If only one file and not a folder, return direct stream
  if (paths.length === 1 && !paths[0].endsWith('/')) {
    const command = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: paths[0],
    });

    try {
      const response = await s3Client.send(command);
      if (!response.Body) {
        throw new Error('No body in response');
      }

      return {
        stream: response.Body as Readable,
        filename: paths[0].split('/').pop() || 'download',
        contentType: response.ContentType || 'application/octet-stream',
        contentLength: response.ContentLength,
      };
    } catch (error) {
      console.error(`Error downloading file '${paths[0]}':`, error);
      throw new Error(`Failed to download file '${paths[0]}': ${error}`);
    }
  }

  // For multiple files or folders, create a zip archive
  const archive = archiver('zip', {
    zlib: { level: 5 },
  });
  const passThrough = new PassThrough();
  archive.pipe(passThrough);

  // Keep track of processed files to avoid duplicates
  const processedFiles = new Set<string>();

  // Find common prefix for all paths to maintain relative structure
  const commonPrefix = paths.reduce((prefix, path) => {
    if (!prefix) return path.split('/').slice(0, -1).join('/');
    while (!path.startsWith(prefix)) {
      prefix = prefix.split('/').slice(0, -1).join('/');
    }
    return prefix;
  }, '');

  // Process each path
  for (const path of paths) {
    if (path.endsWith('/')) {
      // Handle directory
      const command = new ListObjectsV2Command({
        Bucket: BUCKET_NAME,
        Prefix: path,
      });

      const response = await s3Client.send(command);
      if (response.Contents) {
        for (const content of response.Contents) {
          if (!content.Key || content.Key === path) continue;

          // Skip if we've already processed this file
          if (processedFiles.has(content.Key)) continue;
          processedFiles.add(content.Key);

          // console.log('Processing directory file:', content.Key);

          const fileResponse = await s3Client.send(
            new GetObjectCommand({
              Bucket: BUCKET_NAME,
              Key: content.Key,
            }),
          );

          if (fileResponse.Body) {
            // Maintain relative path structure from the common parent
            const relativePath = content.Key.replace(commonPrefix + '/', '');
            archive.append(fileResponse.Body as Readable, { name: relativePath });
          }
        }
      }
    } else {
      // Skip if this file is already included in a processed directory
      if (processedFiles.has(path)) continue;
      processedFiles.add(path);

      // console.log('Processing individual file:', path);

      // Handle single file
      const fileResponse = await s3Client.send(
        new GetObjectCommand({
          Bucket: BUCKET_NAME,
          Key: path,
        }),
      );

      if (fileResponse.Body) {
        // Maintain relative path structure from the common parent
        const relativePath = path.replace(commonPrefix + '/', '');
        archive.append(fileResponse.Body as Readable, { name: relativePath });
      }
    }
  }

  // Finalize the archive
  await archive.finalize();

  return {
    stream: passThrough,
    filename: 'download.zip',
    contentType: 'application/zip',
  };
};

export const modifyTextFile = async (path: string, newContent: string): Promise<void> => {
  // Verify it's a text file
  const contentType = getContentType(path);
  if (!isTextFile(path, contentType)) {
    throw new Error('Only text files can be modified using this function');
  }

  try {
    // Get the existing metadata file if it exists
    let metadata: Buffer | undefined;
    try {
      const metadataCommand = new GetObjectCommand({
        Bucket: BUCKET_NAME,
        Key: `${path}.metadata.json`,
      });
      const metadataResponse = await s3Client.send(metadataCommand);
      if (metadataResponse.Body) {
        metadata = Buffer.from(await (metadataResponse.Body as Readable).read());
      }
    } catch (error) {
      // Metadata file might not exist, which is fine
    }

    // Upload the modified content
    const uploadCommand = new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: path,
      Body: Buffer.from(newContent),
      ContentType: contentType,
    });
    await s3Client.send(uploadCommand);

    const parts = path.split('/').filter(Boolean);
    if (parts[0]) {
      await markIngestionJobAsInStock(parts[0]);
    }

    // Re-upload metadata if it existed
    if (metadata) {
      const metadataUploadCommand = new PutObjectCommand({
        Bucket: BUCKET_NAME,
        Key: `${path}.metadata.json`,
        Body: metadata,
        ContentType: 'application/json',
      });
      await s3Client.send(metadataUploadCommand);
    }
  } catch (error) {
    console.error(`Error modifying text file '${path}':`, error);
    throw new Error(`Failed to modify text file '${path}': ${error}`);
  }
};

export const modifyJsonFile = async (path: string, newContent: any): Promise<void> => {
  // Verify it's a JSON file
  const contentType = getContentType(path);
  if (contentType !== 'application/json') {
    throw new Error('Only JSON files can be modified using this function');
  }

  try {
    // Upload the modified content
    const uploadCommand = new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: path,
      Body: Buffer.from(JSON.stringify(newContent, null, 2)),
      ContentType: contentType,
    });

    await s3Client.send(uploadCommand);
    await updateDocument(
      path.replace(/\.metadata\.json$/, ''),
      newContent?.metadataAttributes || {},
    );
  } catch (error) {
    console.error(`Error modifying JSON file '${path}':`, error);
    throw new Error(`Failed to modify JSON file '${path}': ${error}`);
  }
};

export const readTextFile = async (path: string): Promise<string> => {
  // Verify it's a text file
  const contentType = getContentType(path);
  if (!isTextFile(path, contentType)) {
    throw new Error('Only text files can be read using this function');
  }

  try {
    const command = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: path,
    });

    const response = await s3Client.send(command);
    if (!response.Body) {
      throw new Error('No content found in the file');
    }

    // Convert the readable stream to a string
    const chunks: Uint8Array[] = [];
    for await (const chunk of response.Body as Readable) {
      chunks.push(chunk);
    }
    return Buffer.concat(chunks).toString('utf-8');
  } catch (error) {
    throw error;
  }
};

export const readJsonFile = async (path: string): Promise<any> => {
  // Verify it's a JSON file
  const contentType = getContentType(path);
  if (contentType !== 'application/json') {
    throw new Error('Only JSON files can be read using this function');
  }

  try {
    const command = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: path,
    });

    const response = await s3Client.send(command);
    if (!response.Body) {
      throw new Error('No content found in the file');
    }

    // Convert the readable stream to a string
    const chunks: Uint8Array[] = [];
    for await (const chunk of response.Body as Readable) {
      chunks.push(chunk);
    }
    const jsonString = Buffer.concat(chunks).toString('utf-8');
    return JSON.parse(jsonString);
  } catch (error) {
    console.error(`Error reading JSON file '${path}':`, error);
    throw new Error(`Failed to read JSON file '${path}': ${error}`);
  }
};

export const listS3Folder = async (prefix: string = '') => {
  try {
    const command = new ListObjectsV2Command({
      Bucket: BUCKET_NAME,
      Prefix: prefix,
      Delimiter: '/',
    });
    const { CommonPrefixes } = await s3Client.send(command);

    if (!CommonPrefixes || CommonPrefixes.length === 0) {
      return;
    }

    return Object.fromEntries(CommonPrefixes.map((prefix) => [prefix.Prefix, {}]));
  } catch (error) {
    console.error('❌ Error listing folders:', error);
  }
};

export const moveFileOrFolder = async (oldKey: string, newKey: string): Promise<void> => {
  console.log('🚀 ~ moveFileOrFolder ~ oldKey:', { oldKey, newKey });
  try {
    if (oldKey.endsWith('/')) {
      // Handle directory move
      const command = new ListObjectsV2Command({
        Bucket: BUCKET_NAME,
        Prefix: oldKey,
      });

      const response = await s3Client.send(command);
      if (!response.Contents || response.Contents.length === 0) {
        return;
      }

      let prefix: any = response.Prefix;
      prefix = prefix?.split('/').slice(0, -2).join('/') + '/';
      console.log('🚀 ~ moveFileOrFolder ~ prefix:', prefix);

      // Copy all objects with the new prefix
      for await (const content of response.Contents) {
        console.log('🚀 ~ forawait ~ content:', content);
        if (!content.Key) continue;

        if (content.Key.includes('.metadata.json')) {
          const jsonMetadata = await readJsonFile(content.Key);
          const updateJson = {
            ...jsonMetadata,
            metadataAttributes: {
              ...jsonMetadata.metadataAttributes,
              category: newKey.split('/')[0],
            },
          };
          await modifyJsonFile(content.Key, updateJson);
        }

        const newObjectKey =
          prefix === '/' ? newKey + content.Key : content.Key.replace(prefix, newKey);
        console.log('🚀 ~ forawait ~ newObjectKey:', newObjectKey);
        const copyCommand = new CopyObjectCommand({
          Bucket: BUCKET_NAME,
          CopySource: encodeURIComponent(`${BUCKET_NAME}/${content.Key}`),
          Key: newObjectKey,
        });

        await s3Client.send(copyCommand);
      }
      await updateDocumentByKey(oldKey, newKey, 'key');
      await deleteObjects([oldKey]);
    } else {
      const fileName = oldKey.split('/').pop();

      const copyCommand = new CopyObjectCommand({
        Bucket: BUCKET_NAME,
        CopySource: encodeURIComponent(`${BUCKET_NAME}/${oldKey}`),
        Key: newKey + fileName,
      });

      const copyCommandMetadata = new CopyObjectCommand({
        Bucket: BUCKET_NAME,
        CopySource: encodeURIComponent(`${BUCKET_NAME}/${oldKey + '.metadata.json'}`),
        Key: newKey + fileName + '.metadata.json',
      });

      await s3Client.send(copyCommandMetadata);
      await s3Client.send(copyCommand);

      await updateDocumentByKey(oldKey, newKey, 'key');
      await deleteObjects([oldKey]);
    }
  } catch (error) {
    console.error('Error moving file or folder:', error);
    throw new Error(`Failed to move file or folder from '${oldKey}' to '${newKey}': ${error}`);
  }
};

export const synchronousToDB = async (prefix: string = '') => {
  let objects: string[] = [];

  await initIndexSync();

  const listObjects = async (currentPrefix: string) => {
    let continuationToken: string | undefined = undefined;

    do {
      const command = new ListObjectsV2Command({
        Bucket: BUCKET_NAME,
        Prefix: currentPrefix,
        Delimiter: '/',
        ContinuationToken: continuationToken,
        MaxKeys: 1000,
      });

      try {
        const response: ListObjectsV2CommandOutput = await s3Client.send(command);
        // console.log('🚀 ~ listObjects ~ response:', response);

        objects = objects.concat(response.Contents?.map((obj: _Object) => obj.Key || '') || []);

        if (response.CommonPrefixes) {
          await Promise.all(
            response.CommonPrefixes.map((prefixObj) => prefixObj.Prefix)
              .filter((p): p is string => !!p)
              .map((subPrefix) => listObjects(subPrefix)),
          );
        }

        if (response.Contents && response.Contents.length > 0) {
          const documentArray = response.Contents.filter(
            (element) =>
              element.Size && element.Size > 0 && !element.Key?.includes('.metadata.json'),
          ).map((element) => ({
            id: uuidv4(),
            name: element.Key?.split('/')?.pop() || element.Key || '',
            key: element.Key || '',
            size: element.Size || '',
            description: '',
            userId: '0299b2b0-980a-4d5c-8692-bc69e3b576e2',
            idFolder: false,
            etag: element?.ETag,
            lastModified: element?.LastModified,
            AccessLevel: '',
            KeywordAISuggestion: '',
            DataSource: '',
            SuggestedAICategories: '',
            SummaryOfSuggestedAI: '',
          }));

          await addDocuments(documentArray, indexSync);
        }

        if (response?.Prefix) {
          await addDocuments(
            [
              {
                id: uuidv4(),
                name: response.Prefix,
                key: response.Prefix,
                size: 0,
                description: '',
                userId: '0299b2b0-980a-4d5c-8692-bc69e3b576e2',
                idFolder: true,
                etag: '',
                lastModified: new Date().toISOString(),
                AccessLevel: '',
                KeywordAISuggestion: '',
                DataSource: '',
                SuggestedAICategories: '',
                SummaryOfSuggestedAI: '',
              },
            ],
            indexSync,
          );
        }

        continuationToken = response.NextContinuationToken;
      } catch (error) {
        console.error('Error fetching objects:', error);
        throw error;
      }
    } while (continuationToken);
  };

  await listObjects(prefix);
  await swapIndexes();
  return objects;
};

export const startIngestionJob = async (
  prefix: string,
  user_id: string,
  userName: string,
): Promise<any> => {
  try {
    const parts = prefix.split('/').filter(Boolean);
    const folderName = parts[0];
    if (folderName) {
      const result = await get(
        'SELECT knowledge_base_id, data_source_ids FROM knowledge_base WHERE folder_name = $1',
        [`${folderName}/`],
      );

      const knowledgeBaseId = result[0]?.knowledge_base_id || null;
      const dataSourceId = result[0]?.data_source_ids || null;
      const input = {
        knowledgeBaseId: knowledgeBaseId,
        dataSourceId: dataSourceId,
      };
      if (knowledgeBaseId && dataSourceId) {
        const command = new StartIngestionJobCommand(input);
        const response = await client.send(command);

        const ingestionJobID = response.ingestionJob?.ingestionJobId;

        const existingRecord = await get(
          'SELECT * FROM ingestion_jobs_status WHERE knowledge_base_id = $1 AND data_source_id = $2',
          [knowledgeBaseId, dataSourceId],
        );

        if (!existingRecord || existingRecord.length === 0) {
          await add(
            'INSERT INTO ingestion_jobs_status (ingestion_job_id, knowledge_base_id, data_source_id, user_id, user_name, status, is_still_in_stock) VALUES ($1, $2, $3, $4, $5, $6, $7)',
            [ingestionJobID, knowledgeBaseId, dataSourceId, user_id, userName, 'STARTING', false],
          );
        } else {
          await update(
            'UPDATE ingestion_jobs_status SET ingestion_job_id = $1, status = $2, created_date = $3, is_still_in_stock = $4 WHERE knowledge_base_id = $5 AND data_source_id = $6',
            [ingestionJobID, 'STARTING', new Date(), false, knowledgeBaseId, dataSourceId],
          );
        }

        return response.ingestionJob;
      }
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw errorMessage;
  }
};

export const getIngestionJobStatus = async (prefix: string) => {
  const defaultUser = {
    id: '0299b2b0-980a-4d5c-8692-bc69e3b576e2',
    name: 'cmc_ts_admin',
  };

  const getFolderInfo = async (folderName: string) => {
    const result = await get(
      'SELECT knowledge_base_id, data_source_ids FROM knowledge_base WHERE folder_name = $1',
      [`${folderName}/`],
    );
    return {
      knowledgeBaseId: result[0]?.knowledge_base_id,
      dataSourceId: result[0]?.data_source_ids,
    };
  };

  const getIngestionJobInfo = async (knowledgeBaseId: string, dataSourceId: string) => {
    const command = new ListIngestionJobsCommand({
      knowledgeBaseId,
      dataSourceId,
      sortBy: { attribute: 'STARTED_AT', order: 'DESCENDING' },
      maxResults: 1,
    });
    const response = await client.send(command);

    return response.ingestionJobSummaries?.[0];
  };

  const createStatusResponse = (
    status: string,
    ingestionJobId: string,
    knowledgeBaseId: string,
    dataSourceId: string,
    user = defaultUser,
    is_still_in_stock = false,
    errorMessage = '',
    statistics?: {
      numberOfDocumentsDeleted?: number;
      numberOfDocumentsFailed?: number;
      numberOfDocumentsScanned?: number;
      numberOfMetadataDocumentsModified?: number;
      numberOfMetadataDocumentsScanned?: number;
      numberOfModifiedDocumentsIndexed?: number;
      numberOfNewDocumentsIndexed?: number;
    },
  ) => ({
    status,
    ingestionJobId,
    knowledgeBaseId,
    dataSourceId,
    user_id: user.id,
    user_name: user.name,
    is_still_in_stock,
    created_date: new Date(),
    errorMessage,
    statistics,
  });

  try {
    const folderName = prefix.split('/').filter(Boolean)[0];
    if (!folderName) return;

    const { knowledgeBaseId, dataSourceId } = await getFolderInfo(folderName);
    if (!knowledgeBaseId || !dataSourceId) return;

    const ingestionJob = await getIngestionJobInfo(knowledgeBaseId, dataSourceId);
    const { status = 'COMPLETE', ingestionJobId = 'null', statistics } = ingestionJob || {};

    const existingRecord = await get(
      'SELECT * FROM ingestion_jobs_status WHERE knowledge_base_id = $1 AND data_source_id = $2',
      [knowledgeBaseId, dataSourceId],
    );

    if (existingRecord.length > 0) {
      const record = existingRecord[0];
      const { is_still_in_stock, user_id, user_name } = record;

      if (
        (status === 'COMPLETE' && ingestionJobId === 'null') ||
        (status === 'COMPLETE' && is_still_in_stock)
      ) {
        await startIngestionJob(prefix, user_id, user_name);
        return createStatusResponse(
          'STARTING',
          ingestionJobId,
          knowledgeBaseId,
          dataSourceId,
          { id: user_id, name: user_name },
          is_still_in_stock,
          '',
          statistics,
        );
      }

      if (status && record.status !== status) {
        await update(
          'UPDATE ingestion_jobs_status SET status = $1 WHERE knowledge_base_id = $2 AND data_source_id = $3',
          [status || 'NOT_FOUND', knowledgeBaseId, dataSourceId],
        );
      }

      return createStatusResponse(
        status,
        ingestionJobId,
        knowledgeBaseId,
        dataSourceId,
        { id: user_id, name: user_name },
        is_still_in_stock,
        '',
        statistics,
      );
    }

    await add(
      'INSERT INTO ingestion_jobs_status (ingestion_job_id, knowledge_base_id, data_source_id, user_id, user_name, status, is_still_in_stock, created_date) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)',
      [
        ingestionJobId,
        knowledgeBaseId,
        dataSourceId,
        defaultUser.id,
        defaultUser.name,
        status,
        false,
        new Date(),
      ],
    );

    return createStatusResponse(
      status,
      ingestionJobId,
      knowledgeBaseId,
      dataSourceId,
      undefined,
      undefined,
      '',
      statistics,
    );
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const folderName = prefix.split('/').filter(Boolean)[0];
    if (!folderName) return;

    const { knowledgeBaseId, dataSourceId } = await getFolderInfo(folderName);

    return createStatusResponse(
      'FAILED',
      'null',
      knowledgeBaseId,
      dataSourceId,
      undefined,
      undefined,
      errorMessage,
    );
  }
};

export const markIngestionJobAsInStock = async (folderName: string): Promise<void> => {
  try {
    const result = await get(
      'SELECT knowledge_base_id, data_source_ids FROM knowledge_base WHERE folder_name = $1',
      [`${folderName}/`],
    );

    const knowledgeBaseId = result[0]?.knowledge_base_id || null;
    const dataSourceId = result[0]?.data_source_ids || null;

    if (knowledgeBaseId && dataSourceId) {
      const existingRecord = await get(
        'SELECT * FROM ingestion_jobs_status WHERE knowledge_base_id = $1 AND data_source_id = $2',
        [knowledgeBaseId, dataSourceId],
      );

      if (existingRecord.length > 0 && !existingRecord[0].is_still_in_stock) {
        await update(
          'UPDATE ingestion_jobs_status SET is_still_in_stock = $1 WHERE knowledge_base_id = $2 AND data_source_id = $3',
          [true, knowledgeBaseId, dataSourceId],
        );
      }
    }
  } catch (error) {
    console.error('[ERROR] markIngestionJobAsInStock failed:', error);
    throw new Error(`Failed to mark ingestion job as in stock: ${error}`);
  }
};

export const startSyncKnowledgeBase = async (
  schedule: string,
  prefix: string,
  userId: string,
): Promise<any> => {
  try {
    const createdSchedule = await schedules.create({
      //The id of the scheduled task you want to attach to.
      task: syncS3.id,
      //The schedule in cron format.
      cron: schedule || '0 * * * *',
      timezone: 'Asia/Bangkok',
      //this is required, it prevents you from creating duplicate schedules. It will update the schedule if it already exists.
      deduplicationKey: userId,
      externalId: prefix,
    });
    console.log('🔥 createdSchedule', createdSchedule);
  } catch (error) {
    console.error('[ERROR] Create Sync Knowledge Base failed:', error);
    throw new Error(`Failed to start create sync knowledge base job: ${error}`);
  }
};

export const stopSyncKnowledgeBase = async (scheduleId: string): Promise<any> => {
  try {
    if (scheduleId) {
      const deactivatedSchedule = await schedules.deactivate(scheduleId);
      console.log('🔥 deactivatedSchedule', deactivatedSchedule);
    }
  } catch (error) {
    console.error('[ERROR] Deactive Schedule failed:', error);
    throw new Error(`Failed to deactivate schedule job: ${error}`);
  }
};

export const checkFileExists = async (key: string): Promise<boolean> => {
  try {
    const command = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
    });

    await s3Client.send(command);
    return true;
  } catch (error: any) {
    if (error.name === 'NoSuchKey') {
      return false;
    }
    console.error(`Error checking if file '${key}' exists:`, error);
    throw new Error(`Failed to check if file '${key}' exists: ${error}`);
  }
};
