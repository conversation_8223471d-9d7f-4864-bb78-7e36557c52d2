import { AsyncLocalStorage } from 'async_hooks';
import type { AuditLogOptions } from '../types/audit';

export type RequestContext = {
  username?: string;
  user_id?: string;
  ip_address?: string;
  user_agent?: string;
  session_id?: string;
  request_id?: string;
  endpoint?: string;
  method?: string;
  metadata?: Record<string, any>;
};

class AuditContextManager {
  private static instance: AuditContextManager;
  private asyncLocalStorage: AsyncLocalStorage<RequestContext>;

  private constructor() {
    this.asyncLocalStorage = new AsyncLocalStorage<RequestContext>();
  }

  public static getInstance(): AuditContextManager {
    if (!AuditContextManager.instance) {
      AuditContextManager.instance = new AuditContextManager();
    }
    return AuditContextManager.instance;
  }

  public run<T>(context: RequestContext, callback: () => T): T {
    return this.asyncLocalStorage.run(context, callback);
  }

  public getContext(): RequestContext | undefined {
    return this.asyncLocalStorage.getStore();
  }

  public setContext(context: Partial<RequestContext>): void {
    const currentContext = this.getContext();
    if (currentContext) {
      Object.assign(currentContext, context);
    }
  }

  public getAuditOptions(): AuditLogOptions {
    const context = this.getContext();
    return {
      username: context?.username || 'system',
      user_id: context?.user_id,
      ip_address: context?.ip_address,
      user_agent: context?.user_agent,
      session_id: context?.session_id,
      metadata: {
        request_id: context?.request_id,
        endpoint: context?.endpoint,
        method: context?.method,
        ...context?.metadata,
      },
    };
  }

  public hasContext(): boolean {
    return this.getContext() !== undefined;
  }
}

export const auditContext = AuditContextManager.getInstance();

export function extractUserInfoFromRequest(req: any): Partial<RequestContext> {
  const authHeader = req?.header?.('Authorization') || req?.headers?.['authorization'];
  const accessToken = authHeader?.split(' ')[1];
  
  return {
    ip_address: 
      req?.header?.('x-forwarded-for') || 
      req?.header?.('x-real-ip') || 
      req?.headers?.['x-forwarded-for'] || 
      req?.headers?.['x-real-ip'] || 
      req?.ip,
    user_agent: 
      req?.header?.('user-agent') || 
      req?.headers?.['user-agent'],
    session_id: 
      req?.header?.('x-session-id') || 
      req?.headers?.['x-session-id'] || 
      req?.sessionID,
    request_id: 
      req?.header?.('x-request-id') || 
      req?.headers?.['x-request-id'] || 
      generateRequestId(),
    endpoint: req?.url || req?.path,
    method: req?.method,
    metadata: {
      has_auth_token: !!accessToken,
      content_type: req?.header?.('content-type') || req?.headers?.['content-type'],
    },
  };
}

export function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

export function createAuditMiddleware() {
  return async (c: any, next: any) => {
    const requestInfo = extractUserInfoFromRequest(c.req);
    
    const context: RequestContext = {
      ...requestInfo,
      username: extractUsernameFromRequest(c.req),
      user_id: extractUserIdFromRequest(c.req),
    };

    return auditContext.run(context, async () => {
      await next();
    });
  };
}

function extractUsernameFromRequest(req: any): string {
  const authHeader = req?.header?.('Authorization') || req?.headers?.['authorization'];
  const userHeader = req?.header?.('x-username') || req?.headers?.['x-username'];
  
  if (userHeader) {
    return userHeader;
  }
  
  if (authHeader) {
    try {
      const token = authHeader.split(' ')[1];
      if (token) {
        const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
        return payload.username || payload.sub || payload.email || 'authenticated_user';
      }
    } catch (error) {
      console.debug('Could not parse auth token for username');
    }
  }
  
  return 'anonymous';
}

function extractUserIdFromRequest(req: any): string | undefined {
  const userIdHeader = req?.header?.('x-user-id') || req?.headers?.['x-user-id'];
  
  if (userIdHeader) {
    return userIdHeader;
  }
  
  const authHeader = req?.header?.('Authorization') || req?.headers?.['authorization'];
  if (authHeader) {
    try {
      const token = authHeader.split(' ')[1];
      if (token) {
        const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
        return payload.user_id || payload.sub || payload.id;
      }
    } catch (error) {
      console.debug('Could not parse auth token for user_id');
    }
  }
  
  return undefined;
}
