export type AuditConfig = {
  enabled: boolean;
  logReadOperations: boolean;
  logSystemOperations: boolean;
  backgroundLogging: boolean;
  batchSize: number;
  flushInterval: number;
  maxRetries: number;
  skipTables: string[];
  skipOperations: string[];
  performanceThreshold: number; // ms - log slow queries
  enableDebugLogging: boolean;
};

class AuditConfigManager {
  private static instance: AuditConfigManager;
  private config: AuditConfig;

  private constructor() {
    this.config = this.loadConfig();
  }

  public static getInstance(): AuditConfigManager {
    if (!AuditConfigManager.instance) {
      AuditConfigManager.instance = new AuditConfigManager();
    }
    return AuditConfigManager.instance;
  }

  private loadConfig(): AuditConfig {
    return {
      enabled: process.env.AUDIT_LOGGING_ENABLED !== 'false',
      logReadOperations: process.env.AUDIT_LOG_READ_OPERATIONS === 'true',
      logSystemOperations: process.env.AUDIT_LOG_SYSTEM_OPERATIONS === 'true',
      backgroundLogging: process.env.AUDIT_BACKGROUND_LOGGING !== 'false',
      batchSize: parseInt(process.env.AUDIT_BATCH_SIZE || '10'),
      flushInterval: parseInt(process.env.AUDIT_FLUSH_INTERVAL || '1000'),
      maxRetries: parseInt(process.env.AUDIT_MAX_RETRIES || '3'),
      skipTables: (process.env.AUDIT_SKIP_TABLES || 'audit_logs,pg_stat_activity,information_schema').split(','),
      skipOperations: (process.env.AUDIT_SKIP_OPERATIONS || '').split(',').filter(Boolean),
      performanceThreshold: parseInt(process.env.AUDIT_PERFORMANCE_THRESHOLD || '1000'),
      enableDebugLogging: process.env.AUDIT_DEBUG_LOGGING === 'true',
    };
  }

  public getConfig(): AuditConfig {
    return { ...this.config };
  }

  public updateConfig(updates: Partial<AuditConfig>): void {
    this.config = { ...this.config, ...updates };
  }

  public isEnabled(): boolean {
    return this.config.enabled;
  }

  public shouldLogReadOperations(): boolean {
    return this.config.logReadOperations;
  }

  public shouldLogSystemOperations(): boolean {
    return this.config.logSystemOperations;
  }

  public shouldSkipTable(table: string): boolean {
    return this.config.skipTables.includes(table.toLowerCase());
  }

  public shouldSkipOperation(operation: string): boolean {
    return this.config.skipOperations.includes(operation.toUpperCase());
  }

  public isSlowQuery(executionTime: number): boolean {
    return executionTime > this.config.performanceThreshold;
  }

  public shouldUseBackgroundLogging(): boolean {
    return this.config.backgroundLogging;
  }

  public getBatchSize(): number {
    return this.config.batchSize;
  }

  public getFlushInterval(): number {
    return this.config.flushInterval;
  }

  public getMaxRetries(): number {
    return this.config.maxRetries;
  }

  public isDebugEnabled(): boolean {
    return this.config.enableDebugLogging;
  }
}

export const auditConfig = AuditConfigManager.getInstance();

export function logDebug(message: string, ...args: any[]): void {
  if (auditConfig.isDebugEnabled()) {
    console.debug(`[AUDIT] ${message}`, ...args);
  }
}

export function logInfo(message: string, ...args: any[]): void {
  console.log(`[AUDIT] ${message}`, ...args);
}

export function logError(message: string, ...args: any[]): void {
  console.error(`[AUDIT] ${message}`, ...args);
}
