import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { NodeHttpHandler } from '@smithy/node-http-handler';
import { schedules } from '@trigger.dev/sdk/v3';
import axios from 'axios';
import crypto from 'crypto';
import dotenv from 'dotenv';
import * as mime from 'mime-types';

import { syncOneDrive } from '../trigger/cron/sync-one-drive.ts';
import { add, exists, get, update } from './postgres';
import { deleteObjects, renameObject, uploadMultipleFiles } from './s3';

export const s3Client = new S3Client({
  credentials: {
    accessKeyId: process.env.S3_STORAGE_ACCESS_KEY_ID!,
    secretAccessKey: process.env.S3_STORAGE_SECRET_ACCESS_KEY!,
  },
  region: process.env.S3_STORAGE_REGION || 'us-east-1',
  requestHandler: new NodeHttpHandler({
    connectionTimeout: 600000,
  }),
});
export const BUCKET_NAME = process.env.S3_STORAGE_BUCKET_NAME || 'unknown';
dotenv.config();

const ONEDRIVE_CLIENT_ID = process.env.ONEDRIVE_CLIENT_ID!;
const ONEDRIVE_CLIENT_SECRET = process.env.ONEDRIVE_CLIENT_SECRET!;
const ONEDRIVE_REDIRECT_URI = process.env.ONEDRIVE_REDIRECT_URI!;
const ONEDRIVE_API_URL =
  process.env.ONEDRIVE_API_URL || 'https://graph.microsoft.com/v1.0/me/drive';

export function getAuthUrl(): string {
  return `https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize?client_id=${ONEDRIVE_CLIENT_ID}&response_type=code&redirect_uri=${ONEDRIVE_REDIRECT_URI}&scope=offline_access Files.ReadWrite Files.Read Files.Read.All`;
}

export async function saveRefreshTokenToDB(userId: string, refreshToken: string): Promise<void> {
  const tokenExists = await exists('SELECT 1 FROM user_tokens WHERE user_id = $1 AND type = $2', [
    userId,
    'OneDrive',
  ]);

  if (tokenExists) {
    await update(
      'UPDATE user_tokens SET refresh_token = $2, updated_at = CURRENT_TIMESTAMP WHERE user_id = $1 AND type = $3',
      [userId, refreshToken, 'OneDrive'],
    );
  } else {
    await add('INSERT INTO user_tokens (user_id, refresh_token) VALUES ($1, $2)', [
      userId,
      refreshToken,
    ]);
  }
}

export async function getRefreshTokenFromDB(userId: string): Promise<string> {
  const query = 'SELECT refresh_token FROM user_tokens WHERE user_id = $1 AND type = $2';
  const result = await get(query, [userId, 'OneDrive']);

  if (!result.length) {
    throw new Error('Không tìm thấy refresh_token cho user');
  }

  return result[0].refresh_token;
}

export async function getOneDriveRefreshToken(
  authorizationCode: string,
  userId: string,
): Promise<string> {
  const tokenEndpoint = `https://login.microsoftonline.com/consumers/oauth2/v2.0/token`;

  const params = new URLSearchParams({
    client_id: ONEDRIVE_CLIENT_ID,
    client_secret: ONEDRIVE_CLIENT_SECRET,
    code: authorizationCode,
    grant_type: 'authorization_code',
    redirect_uri: ONEDRIVE_REDIRECT_URI,
    scope: 'offline_access Files.ReadWrite',
  });

  try {
    const response = await axios.post(tokenEndpoint, params);
    const refreshToken = response.data.refresh_token;

    await saveRefreshTokenToDB(userId, refreshToken);

    return refreshToken;
  } catch (error: any) {
    console.error('❌ Lỗi khi lấy refresh token:', error.response?.data || error.message);
    throw new Error('Không thể lấy refresh token');
  }
}

export async function getOneDriveAccessToken(refreshToken: string): Promise<string> {
  const tokenEndpoint = `https://login.microsoftonline.com/consumers/oauth2/v2.0/token`;

  const params = new URLSearchParams({
    client_id: ONEDRIVE_CLIENT_ID,
    client_secret: ONEDRIVE_CLIENT_SECRET,
    refresh_token: refreshToken,
    grant_type: 'refresh_token',
    scope: 'offline_access Files.ReadWrite',
  });

  try {
    const response = await axios.post(tokenEndpoint, params);
    return response.data.access_token;
  } catch (error: any) {
    console.error('❌ Lỗi khi lấy Access Token:', error.response?.data || error.message);
    throw new Error('Không thể lấy Access Token');
  }
}

const processedFolders: Set<string> = new Set();

export async function getOneDriveFiles(
  accessToken: string,
  folderId: string = 'root',
): Promise<any[]> {
  if (processedFolders.has(folderId)) {
  } else {
    processedFolders.add(folderId);
  }

  const url = `${ONEDRIVE_API_URL}/items/${folderId}/children`;

  try {
    const response = await axios.get(url, {
      headers: { Authorization: `Bearer ${accessToken}` },
    });

    const files = response.data.value.map((file: any) => ({
      id: file.id,
      name: file.name,
      size: file.size ?? 0,
      lastModifiedDateTime: file.lastModifiedDateTime,
      type: file.folder ? 'folder' : 'file',
      parentId: folderId,
    }));

    for (const file of files) {
      if (file.type === 'folder' && !processedFolders.has(file.id)) {
        file.children = await getOneDriveFiles(accessToken, file.id);
      }
    }

    return files;
  } catch (error) {
    console.error('❌ Lỗi khi lấy file từ OneDrive:', error);
    throw new Error('Failed to fetch files from OneDrive');
  }
}

export async function downloadOneDriveFile(
  fileId: string,
  accessToken: string,
): Promise<{ buffer: Buffer | null; fileName: string; prefix: string }> {
  const url = `${ONEDRIVE_API_URL}/items/${fileId}/content`;
  const metadataUrl = `${ONEDRIVE_API_URL}/items/${fileId}`;

  try {
    // 1. Lấy metadata của item
    const metadataResponse = await axios.get(metadataUrl, {
      headers: { Authorization: `Bearer ${accessToken}` },
    });

    // Kiểm tra nếu đây là folder, nếu có thì không cần tải nội dung
    const isFolder = metadataResponse.data.folder !== undefined;

    let fileName = 'untitled';
    let prefix = '';

    if (isFolder) {
      fileName = metadataResponse.data.name;
      const parentPath = metadataResponse.data.parentReference.path;

      if (parentPath?.includes(':/')) {
        const parts = parentPath.split(':/');
        prefix = parts[1] + '/' + fileName;
      }
      return { buffer: null, fileName, prefix };
    } else {
      const response = await axios.get(url, {
        responseType: 'arraybuffer',
        headers: { Authorization: `Bearer ${accessToken}` },
      });

      const fileNameHeader = response.headers['content-disposition'];
      if (fileNameHeader?.includes('filename=')) {
        fileName = fileNameHeader.split('filename=')[1].trim().replace(/^"|"$/g, '');
      }

      const parentPath = metadataResponse.data.parentReference.path;
      if (parentPath?.includes(':/')) {
        const parts = parentPath.split(':/');
        prefix = parts[1] + '/' + fileName; // c-agent/test/.../ + fileName
      }

      const buffer = Buffer.from(response.data);
      return { buffer, fileName, prefix };
    }
  } catch (error) {
    console.error(`❌ Lỗi khi tải file ${fileId} từ OneDrive:`, error);
    throw new Error(`Failed to download file ${fileId}`);
  }
}

function calculateFileHash(buffer: Buffer): string {
  const hash = crypto.createHash('sha256');
  hash.update(buffer);
  return hash.digest('hex');
}

async function getOneDriveItem(accessToken: string, itemId: string) {
  const res = await fetch(`https://graph.microsoft.com/v1.0/me/drive/items/${itemId}`, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return await res.json();
}

export async function syncOneDriveFolder(
  folderId: string | 'root',
  accessToken: string,
  parentPrefix = '',
): Promise<string[]> {
  const folderItems = await getOneDriveFiles(accessToken, folderId);

  const uploadedKeys: string[] = [];
  if (folderId !== 'root') {
    const currentFolder = await getOneDriveItem(accessToken, folderId);
    const currentPrefixParts = parentPrefix.split('/');
    const currentName = currentPrefixParts.filter(Boolean).pop() || '';
    const folderPrefix = parentPrefix.slice(0, -`${currentName}/`.length);

    const [existingRecord] = await get(
      `SELECT * FROM onedrive_synced_items WHERE item_id = $1 AND prefix = $2`,
      [folderId, folderPrefix],
    );

    if (!existingRecord) {
      await add(
        `INSERT INTO onedrive_synced_items (item_id, prefix, file_name, updated_at)
           VALUES ($1, $2, $3, $4)`,
        [
          folderId,
          folderPrefix,
          currentName,
          currentFolder.lastModifiedDateTime || new Date().toISOString(),
        ],
      );
      console.log(`📁 Inserted current folder: ${folderPrefix}${currentName}/`);
    } else if (existingRecord.file_name !== currentName) {
      const oldPrefix = `${folderPrefix}${existingRecord.file_name}/`;
      const newPrefix = `${folderPrefix}${currentName}/`;

      await renameObject(oldPrefix, newPrefix);

      await update(
        `UPDATE onedrive_synced_items SET file_name = $1, updated_at = $2 WHERE item_id = $3 AND prefix = $4`,
        [currentName, new Date().toISOString(), folderId, folderPrefix],
      );

      await update(
        `UPDATE onedrive_synced_items SET prefix = regexp_replace(prefix, $1, $2) WHERE prefix LIKE $3`,
        [oldPrefix, newPrefix, `${oldPrefix}%`],
      );

      console.log(`🔤 Folder renamed: ${oldPrefix} → ${newPrefix} (from caller-level folder)`);
      uploadedKeys.push(newPrefix);
    }
  }

  for (const item of folderItems) {
    const itemPath = `${parentPrefix}${item.name}`;
    const isFolder = item.type === 'folder';

    const [existingRecord] = await get(
      `SELECT * FROM onedrive_synced_items WHERE item_id = $1 AND prefix = $2`,
      [item.id, parentPrefix],
    );

    if (isFolder) {
      const folderKey = `${itemPath}/`;
      const metadataKey = `${folderKey}.metadata.json`;
      const metadataContent = {
        metadataAttributes: { DataSource: 'OneDrive' },
      };

      await s3Client.send(
        new PutObjectCommand({
          Bucket: BUCKET_NAME,
          Key: metadataKey,
          Body: JSON.stringify(metadataContent, null, 2),
          ContentType: 'application/json',
        }),
      );
      console.log(`📁 Sync folder: ${folderKey}`);
      console.log(`📄 Uploaded metadata: ${metadataKey}`);

      if (!existingRecord) {
        await add(
          `INSERT INTO onedrive_synced_items (item_id, prefix, file_name, updated_at)
             VALUES ($1, $2, $3, $4)`,
          [item.id, parentPrefix, item.name, item.lastModifiedDateTime || new Date().toISOString()],
        );
        console.log(`📁 Sync new folder: ${parentPrefix}${item.name}/`);
      } else if (existingRecord.file_name !== item.name) {
        const oldPrefix = `${parentPrefix}${existingRecord.file_name}/`;
        const newPrefix = folderKey;
        await renameObject(oldPrefix, newPrefix);
        await update(
          `UPDATE onedrive_synced_items SET file_name = $1, updated_at = $2 WHERE item_id = $3 AND prefix = $4`,
          [item.name, new Date().toISOString(), item.id, parentPrefix],
        );

        await update(
          `UPDATE onedrive_synced_items SET prefix = regexp_replace(prefix, $1, $2) WHERE prefix LIKE $3`,
          [oldPrefix, newPrefix, `${oldPrefix}%`],
        );

        console.log(`🔤 Folder renamed: ${oldPrefix} → ${newPrefix}`);
        uploadedKeys.push(newPrefix);
      }

      const subFolderUploaded = await syncOneDriveFolder(item.id, accessToken, folderKey);
      uploadedKeys.push(...subFolderUploaded);
    } else {
      const { buffer, fileName } = await downloadOneDriveFile(item.id, accessToken);
      if (!buffer) {
        console.warn(`File "${item.name}" could not be downloaded (buffer is null). Skipping.`);
        continue;
      }

      const newHash = calculateFileHash(buffer);
      const newPath = `${parentPrefix}${fileName}`;

      if (!existingRecord) {
        console.log(`🆕 New file detected: ${fileName}`);

        const uploaded = await uploadMultipleFiles([
          {
            prefix: parentPrefix,
            name: fileName,
            file: buffer,
            contentType: mime.lookup(fileName) || 'application/octet-stream',
          },
        ]);

        await add(
          `INSERT INTO onedrive_synced_items (item_id, prefix, file_name, hash, updated_at)
                     VALUES ($1, $2, $3, $4, $5)`,
          [
            item.id,
            parentPrefix,
            fileName,
            newHash,
            item.lastModifiedDateTime || new Date().toISOString(),
          ],
        );
        console.log(`📄 Inserted new file to DB: ${parentPrefix}${fileName} (item_id: ${item.id})`);
        uploadedKeys.push(...uploaded);
      } else {
        const { file_name: oldName, hash: oldHash } = existingRecord;
        const isNameChanged = fileName !== oldName;
        const isContentChanged = newHash !== oldHash;
        const oldPath = `${parentPrefix}${oldName}`;

        if (isNameChanged && isContentChanged) {
          console.log(`♻️ File renamed and content changed: ${oldName} → ${fileName}`);

          await deleteObjects([oldPath]);

          const uploaded = await uploadMultipleFiles([
            {
              prefix: parentPrefix,
              name: fileName,
              file: buffer,
              contentType: mime.lookup(fileName) || 'application/octet-stream',
            },
          ]);

          await update(
            `UPDATE onedrive_synced_items SET file_name = $1, hash = $2, updated_at = $3 WHERE item_id = $4 AND prefix = $5`,
            [fileName, newHash, new Date().toISOString(), item.id, parentPrefix],
          );

          uploadedKeys.push(...uploaded);
        } else if (isNameChanged) {
          console.log(`🔤 File renamed: ${oldName} → ${fileName}`);

          await renameObject(oldPath, newPath);

          await update(
            `UPDATE onedrive_synced_items SET file_name = $1, updated_at = $2 WHERE item_id = $3 AND prefix = $4`,
            [fileName, new Date().toISOString(), item.id, parentPrefix],
          );

          uploadedKeys.push(newPath);
        } else if (isContentChanged) {
          console.log(`📝 File content updated: ${fileName}`);

          const uploaded = await uploadMultipleFiles([
            {
              prefix: parentPrefix,
              name: fileName,
              file: buffer,
              contentType: mime.lookup(fileName) || 'application/octet-stream',
            },
          ]);

          await update(
            `UPDATE onedrive_synced_items SET hash = $1, updated_at = $2 WHERE item_id = $3 AND prefix = $4`,
            [newHash, new Date().toISOString(), item.id, parentPrefix],
          );

          uploadedKeys.push(...uploaded);
        } else {
          console.log(`✅ File unchanged: ${fileName}`);
        }
      }
    }
  }

  return uploadedKeys;
}

export async function syncOneDriveFile(
  file: any,
  accessToken: string,
  prefix: string,
): Promise<string[]> {
  const uploadedKeys: string[] = [];

  const { buffer, fileName } = await downloadOneDriveFile(file.id, accessToken);
  if (!buffer) {
    console.warn(`File "${file.name}" could not be downloaded (buffer is null). Skipping.`);
    return uploadedKeys;
  }

  const newHash = calculateFileHash(buffer);
  const newPath = `${prefix}${fileName}`;

  const [existingRecord] = await get(
    `SELECT * FROM onedrive_synced_items WHERE item_id = $1 AND prefix = $2`,
    [file.id, prefix],
  );

  if (!existingRecord) {
    console.log(`🆕 New file detected (manual upload): ${fileName}`);

    const uploaded = await uploadMultipleFiles([
      {
        prefix,
        name: fileName,
        file: buffer,
        contentType: mime.lookup(fileName) || 'application/octet-stream',
      },
    ]);

    await add(
      `INSERT INTO onedrive_synced_items (item_id, prefix, file_name, hash, updated_at)
       VALUES ($1, $2, $3, $4, $5)`,
      [file.id, prefix, fileName, newHash, file.lastModifiedDateTime || new Date().toISOString()],
    );

    uploadedKeys.push(...uploaded);
  } else {
    const { file_name: oldName, hash: oldHash } = existingRecord;
    const isNameChanged = fileName !== oldName;
    const isContentChanged = newHash !== oldHash;
    const oldPath = `${prefix}${oldName}`;

    if (isNameChanged && isContentChanged) {
      console.log(`♻️ File renamed + content changed: ${oldName} → ${fileName}`);

      await deleteObjects([oldPath]);

      const uploaded = await uploadMultipleFiles([
        {
          prefix,
          name: fileName,
          file: buffer,
          contentType: mime.lookup(fileName) || 'application/octet-stream',
        },
      ]);

      await update(
        `UPDATE onedrive_synced_items SET file_name = $1, hash = $2, updated_at = $3 WHERE item_id = $4 AND prefix = $5`,
        [fileName, newHash, new Date().toISOString(), file.id, prefix],
      );

      uploadedKeys.push(...uploaded);
    } else if (isNameChanged) {
      console.log(`🔤 File renamed: ${oldName} → ${fileName}`);

      await renameObject(oldPath, newPath);

      await update(
        `UPDATE onedrive_synced_items SET file_name = $1, updated_at = $2 WHERE item_id = $3 AND prefix = $4`,
        [fileName, new Date().toISOString(), file.id, prefix],
      );

      uploadedKeys.push(newPath);
    } else if (isContentChanged) {
      console.log(`📝 File content updated: ${fileName}`);

      const uploaded = await uploadMultipleFiles([
        {
          prefix,
          name: fileName,
          file: buffer,
          contentType: mime.lookup(fileName) || 'application/octet-stream',
        },
      ]);

      await update(
        `UPDATE onedrive_synced_items SET hash = $1, updated_at = $2 WHERE item_id = $3 AND prefix = $4`,
        [newHash, new Date().toISOString(), file.id, prefix],
      );

      uploadedKeys.push(...uploaded);
    } else {
      console.log(`✅ File unchanged: ${fileName}`);
    }
  }

  return uploadedKeys;
}

export async function resyncSyncedItems(accessToken: string) {
  const syncedItems = await get(
    `SELECT item_id, prefix, hash, file_name FROM onedrive_synced_items`,
    [],
  );

  syncedItems.sort((a, b) => {
    const depthA = (a.prefix + a.file_name).split('/').length;
    const depthB = (b.prefix + b.file_name).split('/').length;
    return depthB - depthA;
  });

  for (const item of syncedItems) {
    const { item_id, prefix, hash: storedHash, file_name: storedFileName } = item;

    if (!storedHash) {
      const { fileName: newFileName } = await downloadOneDriveFile(item_id, accessToken);
      const isNameChanged = newFileName !== storedFileName;

      if (isNameChanged) {
        const folderPrefix = prefix;
        const oldFullPrefix = `${folderPrefix}${storedFileName}/`;
        const newFullPrefix = `${folderPrefix}${newFileName}/`;

        await renameObject(oldFullPrefix, newFullPrefix);
        console.log('📁 Folder renamed:', storedFileName, '→', newFileName);

        await update(
          `UPDATE onedrive_synced_items SET updated_at = $1, file_name = $2 WHERE item_id = $3 AND prefix = $4`,
          [new Date().toISOString(), newFileName, item_id, folderPrefix],
        );

        await update(
          `UPDATE onedrive_synced_items SET prefix = regexp_replace(prefix, $1, $2) WHERE prefix LIKE $3`,
          [oldFullPrefix, newFullPrefix, `${oldFullPrefix}%`],
        );
      }
      continue;
    }

    const { buffer, fileName: newFileName } = await downloadOneDriveFile(item_id, accessToken);
    if (!buffer) continue;

    const newHash = crypto.createHash('sha256').update(buffer).digest('hex');
    const isNameChanged = newFileName !== storedFileName;
    const isContentChanged = newHash !== storedHash;

    const currentPath = `${prefix}${storedFileName}`;
    const newPath = `${prefix}${newFileName}`;

    if (isNameChanged && !isContentChanged) {
      await renameObject(currentPath, newPath);

      await update(
        `UPDATE onedrive_synced_items SET updated_at = $1, file_name = $2 WHERE item_id = $3 AND prefix = $4`,
        [new Date().toISOString(), newFileName, item_id, prefix],
      );
      console.log(`🔤 File renamed: ${storedFileName} → ${newFileName}`);
    } else if (!isNameChanged && isContentChanged) {
      await uploadMultipleFiles([
        {
          prefix: currentPath,
          name: storedFileName,
          file: buffer,
          contentType: mime.lookup(storedFileName) || 'application/octet-stream',
        },
      ]);
      await update(
        `UPDATE onedrive_synced_items SET updated_at = $1, hash = $2 WHERE item_id = $3 AND prefix = $4`,
        [new Date().toISOString(), newHash, item_id, prefix],
      );
      console.log(`📝 File content updated: ${storedFileName}`);
    } else if (isNameChanged && isContentChanged) {
      await deleteObjects([currentPath]);
      await uploadMultipleFiles([
        {
          prefix: newPath,
          name: newFileName,
          file: buffer,
          contentType: mime.lookup(newFileName) || 'application/octet-stream',
        },
      ]);
      await update(
        `UPDATE onedrive_synced_items SET updated_at = $1, hash = $2, file_name = $3 WHERE item_id = $4 AND prefix = $5`,
        [new Date().toISOString(), newHash, newFileName, item_id, prefix],
      );
      console.log(`♻️ File renamed and content updated: ${storedFileName} → ${newFileName}`);
    }
  }

  console.log(`✅ Hoàn tất resync OneDrive`);
}

export const startSyncOneDrive = async (schedule: string, userId: string): Promise<any> => {
  try {
    const createdSchedule = await schedules.create({
      task: syncOneDrive.id,
      cron: schedule || '0 * * * *',
      timezone: 'Asia/Bangkok',
      deduplicationKey: userId,
    });
    console.log('🔥 createdSchedule', createdSchedule);
  } catch (error) {
    console.error('[ERROR] Create Sync Knowledge Base failed:', error);
    throw new Error(`Failed to start create sync knowledge base job: ${error}`);
  }
};

export const stopSyncOneDrive = async (scheduleId: string): Promise<any> => {
  try {
    if (scheduleId) {
      const deactivatedSchedule = await schedules.deactivate(scheduleId);
      console.log('🔥 deactivatedSchedule', deactivatedSchedule);
    }
  } catch (error) {
    console.error('[ERROR] Deactive Schedule failed:', error);
    throw new Error(`Failed to deactivate schedule job: ${error}`);
  }
};
