import { auditContext } from './audit-context';
import { backgroundLogger } from './background-logger';
import { add, get, remove, update } from './postgres';

export async function testAuditLogging(): Promise<void> {
  console.log('🧪 Testing Automatic Audit Logging System...');

  // Set up test context
  const testContext = {
    username: 'test_user',
    user_id: 'test_123',
    ip_address: '127.0.0.1',
    user_agent: 'Test Agent',
    session_id: 'test_session',
    request_id: 'test_req_123',
    endpoint: '/api/test',
    method: 'POST',
  };

  try {
    await auditContext.run(testContext, async () => {
      console.log('📝 Testing CREATE operation...');
      
      // Test CREATE operation
      try {
        await add(
          'INSERT INTO test_table (name, value) VALUES ($1, $2) RETURNING id',
          ['test_item', 'test_value']
        );
        console.log('✅ CREATE operation completed');
      } catch (error) {
        console.log('ℹ️  CREATE test skipped (table may not exist):', error.message);
      }

      console.log('📖 Testing READ operation...');
      
      // Test READ operation
      try {
        await get(
          'SELECT * FROM knowledge_base LIMIT 1',
          []
        );
        console.log('✅ READ operation completed');
      } catch (error) {
        console.log('ℹ️  READ test skipped (table may not exist):', error.message);
      }

      console.log('✏️  Testing UPDATE operation...');
      
      // Test UPDATE operation
      try {
        await update(
          'UPDATE text_summary SET text_summary = $1 WHERE id = $2',
          ['updated_summary', '1']
        );
        console.log('✅ UPDATE operation completed');
      } catch (error) {
        console.log('ℹ️  UPDATE test skipped (table may not exist):', error.message);
      }

      console.log('🗑️  Testing DELETE operation...');
      
      // Test DELETE operation
      try {
        await remove(
          'DELETE FROM test_table WHERE name = $1',
          ['test_item']
        );
        console.log('✅ DELETE operation completed');
      } catch (error) {
        console.log('ℹ️  DELETE test skipped (table may not exist):', error.message);
      }
    });

    // Wait a moment for background logging to process
    console.log('⏳ Waiting for background logging to process...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Check queue status
    const stats = backgroundLogger.getStats();
    console.log('📊 Background Logger Stats:', stats);

    // Flush any remaining logs
    await backgroundLogger.flush();
    console.log('🔄 Background logger flushed');

    console.log('✅ Audit logging test completed successfully!');
    
  } catch (error) {
    console.error('❌ Audit logging test failed:', error);
  }
}

export async function testAuditContext(): Promise<void> {
  console.log('🧪 Testing Audit Context System...');

  // Test without context
  console.log('📝 Testing without context...');
  const noContext = auditContext.getContext();
  console.log('No context result:', noContext);

  // Test with context
  console.log('📝 Testing with context...');
  const testContext = {
    username: 'test_user',
    user_id: 'test_123',
    ip_address: '127.0.0.1',
  };

  await auditContext.run(testContext, async () => {
    const context = auditContext.getContext();
    console.log('Context result:', context);

    const auditOptions = auditContext.getAuditOptions();
    console.log('Audit options:', auditOptions);
  });

  console.log('✅ Audit context test completed!');
}

export async function runAllTests(): Promise<void> {
  console.log('🚀 Running All Audit System Tests...\n');

  await testAuditContext();
  console.log('');
  
  await testAuditLogging();
  console.log('');

  console.log('🎉 All tests completed!');
}

// Export for manual testing
export { testAuditLogging as default };
