import { google } from 'googleapis';
import * as mime from 'mime-types';

import { uploadMultipleFiles } from './s3.ts';

const CLIENT_ID =
  process.env.DRIVE_CLIENT_ID ||
  '726815747852-5deq413t8kd8j3mgj48d46eatjauapq5.apps.googleusercontent.com';
const CLIENT_SECRET = process.env.DRIVE_CLIENT_SECRET || '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';
const REDIRECT_URI = process.env.DRIVE_REDIRECT_URI || 'http://localhost:5173/redirect';
const SCOPES = ['https://www.googleapis.com/auth/drive.readonly'];

const oAuth2Client = new google.auth.OAuth2(CLIENT_ID, CLIENT_SECRET, REDIRECT_URI);

export async function ensureAccessToken(accessToken?: string) {
  if (!accessToken) {
    throw new Error('Không có access token. Vui lòng đăng nhập lại.');
  }

  oAuth2Client.setCredentials({ access_token: accessToken });

  if (oAuth2Client.credentials.expiry_date && oAuth2Client.credentials.expiry_date < Date.now()) {
    console.log('🔄 Access token hết hạn. Đang làm mới...');
    await refreshAccessToken();
  }

  return google.drive({ version: 'v3', auth: oAuth2Client });
}

async function refreshAccessToken() {
  const refreshToken = oAuth2Client.credentials.refresh_token;
  if (!refreshToken) {
    throw new Error('Không có refresh token. Vui lòng đăng nhập lại.');
  }

  const { credentials } = await oAuth2Client.refreshAccessToken();
  oAuth2Client.setCredentials(credentials);
}

async function getDriveService() {
  if (!oAuth2Client.credentials.access_token) {
    throw new Error('Access token chưa được thiết lập. Vui lòng đăng nhập.');
  }
  return google.drive({ version: 'v3', auth: oAuth2Client });
}

export async function downloadFile(fileId: string): Promise<{ buffer: Buffer; fileName: string }> {
  const drive = await getDriveService();
  try {
    const fileMetadata = await drive.files.get({
      fileId: fileId,
      fields: 'mimeType, name',
    });

    const fileMimeType = fileMetadata.data.mimeType as string;
    let fileName = fileMetadata.data.name || 'untitled';

    const exportMimeTypes: { [key: string]: string } = {
      'application/vnd.google-apps.document':
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // DOCX
      'application/vnd.google-apps.spreadsheet':
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // XLSX
      'application/vnd.google-apps.presentation':
        'application/vnd.openxmlformats-officedocument.presentationml.presentation', // PPTX
      'application/vnd.google-apps.drawing': 'image/png', // PNG
    };

    let response: any;

    if (exportMimeTypes[fileMimeType]) {
      const exportMimeType = exportMimeTypes[fileMimeType];
      response = await drive.files.export(
        { fileId, mimeType: exportMimeType },
        { responseType: 'arraybuffer' },
      );

      const extension = mime.extension(exportMimeType);
      if (extension && !fileName.endsWith(`.${extension}`)) {
        fileName += `.${extension}`;
      }
    } else {
      response = await drive.files.get({ fileId, alt: 'media' }, { responseType: 'arraybuffer' });

      const extension = mime.extension(fileMimeType);
      if (extension && !fileName.endsWith(`.${extension}`)) {
        fileName += `.${extension}`;
      }
    }

    if (response.status !== 200) {
      throw new Error(`Không thể tải file. Status: ${response.status}`);
    }

    const buffer = Buffer.from(response.data);
    return { buffer, fileName };
  } catch (error: any) {
    console.error(`❌ Lỗi khi tải file ${fileId}:`, error.message);
    throw error;
  }
}

export async function listFilesInFolder(folderId: string) {
  const drive = await getDriveService();
  try {
    const response = await drive.files.list({
      q: `'${folderId}' in parents and trashed = false`,
      fields: 'files(id, name, mimeType)',
    });

    return response.data.files || [];
  } catch (error) {
    console.error(`❌ Lỗi khi lấy danh sách file trong thư mục ${folderId}:`, error);
    return [];
  }
}

export async function syncFolder(folderId: string, parentPrefix = '') {
  const folderItems = await listFilesInFolder(folderId);
  const uploadedKeys: string[] = [];

  for (const item of folderItems) {
    if (!item.id || !item.name) {
      console.warn('Item missing id or name', item);
      continue;
    }

    if (item.mimeType === 'application/vnd.google-apps.folder') {
      const subFolderPrefix = `${parentPrefix}${item.name}/`;
      const subFolderUploadedKeys = await syncFolder(item.id, subFolderPrefix);
      uploadedKeys.push(...subFolderUploadedKeys);
    } else {
      const fileBuffer = await downloadFile(item.id);
      const uploadedKey = await uploadMultipleFiles([
        {
          prefix: parentPrefix,
          name: item.name,
          file: Buffer.from(fileBuffer as any),
          contentType: item.mimeType ?? 'application/octet-stream',
        },
      ]);
      uploadedKeys.push(...uploadedKey);
    }
  }

  return uploadedKeys;
}
