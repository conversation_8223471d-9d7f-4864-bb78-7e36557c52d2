import { Pool } from 'pg';
import { auditContext } from './audit-context';
import { logAuditEntryAsync } from './background-logger';
import { SQLAnalyzer } from './sql-analyzer';
import type { AuditLogEntry } from '../types/audit';

export const pool = new Pool({
  user: process.env.DATABASE_USER,
  host: process.env.DATABASE_HOST,
  database: process.env.DATABASE_NAME,
  password: process.env.DATABASE_PASSWORD,
  port: Number(process.env.DATABASE_PORT) || 5432,
});

export const connect = async () => {
  try {
    await pool.connect();
    console.log('Connected to the database');
  } catch (err) {
    console.error('Error connecting to the database', err);
    throw err;
  }
};

export const retryConnect = async (retries = 5) => {
  while (retries) {
    try {
      await connect();
      break;
    } catch (err) {
      retries -= 1;
      console.log(`Retries left: ${retries}`);
      if (!retries) throw err;
      await new Promise((res) => setTimeout(res, 5000));
    }
  }
};

async function logOperation(
  query: string,
  values: any[],
  result: any,
  error?: any,
  executionTime?: number
): Promise<void> {
  try {
    if (!auditContext.hasContext()) {
      return;
    }

    const sqlOperation = SQLAnalyzer.parseSQL(query);
    if (!sqlOperation || SQLAnalyzer.shouldSkipAuditLogging(sqlOperation)) {
      return;
    }

    const auditInfo = SQLAnalyzer.extractAuditInfo(sqlOperation, values, result);
    const auditOptions = auditContext.getAuditOptions();
    
    let changes: Record<string, any> | undefined;
    
    if (sqlOperation.type === 'INSERT') {
      const data = SQLAnalyzer.extractDataFromValues(sqlOperation, values);
      changes = {
        after: data,
        fields_changed: Object.keys(data),
      };
    } else if (sqlOperation.type === 'UPDATE') {
      const beforeData = await getBeforeUpdateData(sqlOperation, values);
      const afterData = SQLAnalyzer.extractDataFromValues(sqlOperation, values);
      
      if (beforeData) {
        changes = calculateChanges(beforeData, afterData);
      } else {
        changes = {
          after: afterData,
          fields_changed: Object.keys(afterData),
        };
      }
    } else if (sqlOperation.type === 'DELETE') {
      const beforeData = await getBeforeUpdateData(sqlOperation, values);
      if (beforeData) {
        changes = {
          before: beforeData,
          fields_changed: Object.keys(beforeData),
        };
      }
    }

    const auditEntry: Omit<AuditLogEntry, 'id' | 'created_at' | 'updated_at'> = {
      action: auditInfo.operation_type,
      target_type: auditInfo.target_type,
      target_id: auditInfo.target_id,
      target_name: auditInfo.target_name,
      username: auditOptions.username,
      user_id: auditOptions.user_id,
      changes,
      metadata: {
        ...auditOptions.metadata,
        table: sqlOperation.table,
        execution_time_ms: executionTime,
        query_type: sqlOperation.type,
        has_where_clause: !!sqlOperation.whereClause,
        has_returning_clause: !!sqlOperation.returningClause,
      },
      ip_address: auditOptions.ip_address,
      user_agent: auditOptions.user_agent,
      session_id: auditOptions.session_id,
      error_message: error?.message,
      success: !error,
    };

    await logAuditEntryAsync(auditEntry);
  } catch (logError) {
    console.error('Failed to log audit entry:', logError);
  }
}

async function getBeforeUpdateData(sqlOperation: any, values: any[]): Promise<Record<string, any> | null> {
  try {
    if (!sqlOperation.whereClause || !values.length) {
      return null;
    }

    const selectQuery = `SELECT * FROM ${sqlOperation.table} WHERE ${sqlOperation.whereClause}`;
    const result = await pool.query(selectQuery, values.slice(-1));
    return result.rows[0] || null;
  } catch (error) {
    console.debug('Could not fetch before data:', error);
    return null;
  }
}

function calculateChanges(before: Record<string, any>, after: Record<string, any>): Record<string, any> {
  const changes: Record<string, any> = {
    before: {},
    after: {},
    fields_changed: [],
  };

  const allKeys = new Set([...Object.keys(before), ...Object.keys(after)]);

  for (const key of Array.from(allKeys)) {
    const beforeValue = before[key];
    const afterValue = after[key];

    if (!isEqual(beforeValue, afterValue)) {
      changes.fields_changed.push(key);
      changes.before[key] = beforeValue;
      changes.after[key] = afterValue;
    }
  }

  return changes;
}

function isEqual(a: any, b: any): boolean {
  if (a === b) return true;
  if (a == null || b == null) return a === b;
  if (typeof a !== typeof b) return false;
  if (typeof a === 'object') {
    return JSON.stringify(a) === JSON.stringify(b);
  }
  return false;
}

export const add = async (query: string, values: any[]) => {
  const startTime = Date.now();
  try {
    const res = await pool.query(query, values);
    const executionTime = Date.now() - startTime;
    
    setImmediate(() => {
      logOperation(query, values, res.rows, undefined, executionTime);
    });
    
    return res.rows;
  } catch (err) {
    const executionTime = Date.now() - startTime;
    console.error('Error executing add query', err);
    
    setImmediate(() => {
      logOperation(query, values, null, err, executionTime);
    });
    
    throw err;
  }
};

export const update = async (query: string, values: any[]) => {
  const startTime = Date.now();
  try {
    const res = await pool.query(query, values);
    const executionTime = Date.now() - startTime;
    
    setImmediate(() => {
      logOperation(query, values, res, undefined, executionTime);
    });
    
    return res;
  } catch (err) {
    const executionTime = Date.now() - startTime;
    console.error('Error executing update query', err);
    
    setImmediate(() => {
      logOperation(query, values, null, err, executionTime);
    });
    
    throw err;
  }
};

export const remove = async (query: string, values: any[]) => {
  const startTime = Date.now();
  try {
    const res = await pool.query(query, values);
    const executionTime = Date.now() - startTime;
    
    setImmediate(() => {
      logOperation(query, values, res.rowCount, undefined, executionTime);
    });
    
    return res.rowCount;
  } catch (err) {
    const executionTime = Date.now() - startTime;
    console.error('Error executing delete query', err);
    
    setImmediate(() => {
      logOperation(query, values, null, err, executionTime);
    });
    
    throw err;
  }
};

export const get = async (query: string, values: any[]) => {
  const startTime = Date.now();
  try {
    const res = await pool.query(query, values);
    const executionTime = Date.now() - startTime;
    
    setImmediate(() => {
      logOperation(query, values, res.rows, undefined, executionTime);
    });
    
    return res.rows;
  } catch (err) {
    const executionTime = Date.now() - startTime;
    console.error('Error executing get query', err);
    
    setImmediate(() => {
      logOperation(query, values, null, err, executionTime);
    });
    
    throw err;
  }
};

export const exists = async (query: string, values: any[]): Promise<boolean> => {
  const startTime = Date.now();
  try {
    const res = await pool.query(query, values);
    const executionTime = Date.now() - startTime;
    const result = typeof res.rowCount === 'number' && res.rowCount > 0;
    
    setImmediate(() => {
      logOperation(query, values, result, undefined, executionTime);
    });
    
    return result;
  } catch (err) {
    const executionTime = Date.now() - startTime;
    console.error('Error executing exists query', err);
    
    setImmediate(() => {
      logOperation(query, values, null, err, executionTime);
    });
    
    throw err;
  }
};
