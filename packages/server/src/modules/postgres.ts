import { Pool } from 'pg';

export const pool = new Pool({
  user: process.env.DATABASE_USER,
  host: process.env.DATABASE_HOST,
  database: process.env.DATABASE_NAME,
  password: process.env.DATABASE_PASSWORD,
  port: Number(process.env.DATABASE_PORT) || 5432,
});

export const connect = async () => {
  try {
    await pool.connect();
    console.log('Connected to the database');
  } catch (err) {
    console.error('Error connecting to the database', err);
    throw err;
  }
};

export const retryConnect = async (retries = 5) => {
  while (retries) {
    try {
      await connect();
      break;
    } catch (err) {
      retries -= 1;
      console.log(`Retries left: ${retries}`);
      if (!retries) throw err;
      await new Promise((res) => setTimeout(res, 5000));
    }
  }
};

export const add = async (query: string, values: any[]) => {
  try {
    const res = await pool.query(query, values);
    return res.rows;
  } catch (err) {
    console.error('Error executing add query', err);
    throw err;
  }
};

export const update = async (query: string, values: any[]) => {
  try {
    const res = await pool.query(query, values);
    return res;
  } catch (err) {
    console.error('Error executing update query', err);
    throw err;
  }
};

export const remove = async (query: string, values: any[]) => {
  try {
    const res = await pool.query(query, values);
    return res.rowCount;
  } catch (err) {
    console.error('Error executing delete query', err);
    throw err;
  }
};

export const get = async (query: string, values: any[]) => {
  try {
    const res = await pool.query(query, values);
    return res.rows;
  } catch (err) {
    console.error('Error executing get query', err);
    throw err;
  }
};

export const exists = async (query: string, values: any[]): Promise<boolean> => {
  try {
    const res = await pool.query(query, values);
    return typeof res.rowCount === 'number' && res.rowCount > 0;
  } catch (err) {
    console.error('Error executing exists query', err);
    throw err;
  }
};
