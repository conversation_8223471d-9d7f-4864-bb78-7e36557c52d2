import { MeiliSearch, type SearchParams } from 'meilisearch';

import { getContentType } from './s3';

const client = new MeiliSearch({
  host: process.env.MEILISEARCH_HOST!,
  apiKey: process.env.MEILISEARCH_API_KEY!,
});

const indexName = `documents_${process.env.S3_STORAGE_ACCESS_KEY_ID || process.env.AWS_ACCESS_KEY_ID}`;
export const indexSync = `${indexName}_synch`;
const desiredFilterableAttributes = [
  'userId',
  'visibility',
  'allowedGroups',
  'allowedChatFlows',
  'allowedUserIds',
];

export const createIndex = async (index = indexName) => {
  try {
    const response = await client.createIndex(index, { primaryKey: 'id' });
    return response;
  } catch (error) {
    console.error('Error creating index:', error);
  }
};

export const deleteIndex = async (index = indexName) => {
  try {
    const response = await client.deleteIndex(index);
    return response;
  } catch (error) {
    console.error('Error deleting index:', error);
  }
};

export const getTaskUid = async (taskUid: number) => {
  try {
    const response = await client.getTask(taskUid);
    return response;
  } catch (error) {
    console.error('Error getting task UID:', error);
  }
};

export const deleteDocumentsByField = async (value: string) => {
  try {
    const index = client.index(indexName);
    const searchResponse = await index.search('', {
      showRankingScore: true,
      rankingScoreThreshold: 0.9,
      q: value,
    });

    if (searchResponse.hits.length > 0) {
      const documentIds = searchResponse.hits
        .filter((doc) => doc?.key?.includes(value))
        .map((doc: any) => doc.id);
      const deleteResponse = await index.deleteDocuments(documentIds);
      return deleteResponse;
    } else {
      console.log('No documents found with the specified field and value.');
      return null;
    }
  } catch (error) {
    console.error('Error deleting documents by field:', error);
  }
};

export const updateDocument = async (key: string, newDocument: any) => {
  try {
    const index = client.index(indexName);
    const searchResponse = await index.search(key, {
      rankingScoreThreshold: 0.9,
      q: key,
      limit: 1,
    });

    if (searchResponse?.hits.length <= 0) {
      return;
    }
    const document = searchResponse?.hits.filter((doc) => doc.key === key)[0];

    const mergeObjects = (
      obj1: Record<string, any>,
      obj2: Record<string, any>,
    ): Record<string, any> => {
      return Object?.keys(obj1).reduce(
        (acc, key) => {
          if (obj2.hasOwnProperty(key) && obj2[key]?.trim?.()) {
            acc[key] = obj2[key];
          } else {
            acc[key] = obj1[key];
          }
          return acc;
        },
        {} as Record<string, any>,
      );
    };

    const result: any = mergeObjects(document, newDocument);

    newDocument?.visibility?.trim?.() && (result.visibility = newDocument.visibility);
    newDocument?.allowedGroups && (result.allowedGroups = newDocument.allowedGroups);
    newDocument?.allowedChatFlows && (result.allowedChatFlows = newDocument.allowedChatFlows);

    const response = await index.updateDocuments(result, { primaryKey: 'id' });
    return response;
  } catch (error) {
    console.error('Error updating document:', error);
  }
};

export const updateDocumentByKey = async (oldKey: string, newkey: any, key?: string) => {
  try {
    const index = client.index(indexName);
    const searchResponse = await index.search(oldKey, {
      rankingScoreThreshold: 0.9,
      q: oldKey,
    });

    if (searchResponse?.hits.length <= 0) {
      return;
    }

    if (oldKey.endsWith('/')) {
      let prefix: any = oldKey;
      prefix = prefix?.split('/').slice(0, -2).join('/') + '/';

      const documents = searchResponse?.hits
        .filter((doc) => doc.key.includes(oldKey))
        .map((doc: any) => {
          return {
            ...doc,
            ...(doc.idFolder === true && { name: doc.key.replace(prefix, newkey) }),
            ...(key === 'key' && { key: doc.key.replace(prefix, newkey) }),
          };
        });
      if (documents.length > 0) {
        const response = await index.updateDocuments(documents, { primaryKey: 'id' });
        return response;
      }
    } else {
      let prefix: any = oldKey;
      prefix = oldKey.split('/').pop();
      const documents = searchResponse?.hits
        .filter((doc) => doc.key === oldKey)
        .map((doc: any) => {
          return {
            ...doc,
            ...(key === 'key' && { key: newkey + prefix }),
          };
        });
      if (documents.length > 0) {
        const response = await index.updateDocuments(documents, { primaryKey: 'id' });
        return response;
      }
    }
  } catch (error) {
    console.error('Error updating document:', error);
  }
};

export const updateDocumentByName = async (oldKey: string, newkey: any, key?: string) => {
  try {
    const index = client.index(indexName);
    const searchResponse = await index.search(oldKey, {
      rankingScoreThreshold: 0.9,
      q: oldKey,
    });

    if (searchResponse?.hits.length <= 0) {
      return;
    }

    if (oldKey.endsWith('/')) {
      const documents = searchResponse?.hits
        .filter((doc) => doc.key.includes(oldKey))
        .map((doc: any) => {
          return {
            ...doc,
            ...(doc.idFolder === true && { name: newkey }),
            ...(key === 'key' && { key: doc.key.replace(oldKey, newkey) }),
          };
        });

      if (documents.length > 0) {
        const response = await index.updateDocuments(documents, { primaryKey: 'id' });
        return response;
      }
    } else {
      const name = newkey.split('/').pop();
      const documents = searchResponse?.hits
        .filter((doc) => doc.key === oldKey)
        .map((doc: any) => {
          return {
            ...doc,
            name,
            key: newkey,
          };
        });

      if (documents.length > 0) {
        const response = await index.updateDocuments(documents, { primaryKey: 'id' });
        return response;
      }
    }
  } catch (error) {
    console.error('Error updating document:', error);
  }
};

interface Document {
  id: string;
  name: string;
  key: string;
  size: number | string;
  description?: string;
  userId: string;
  idFolder: boolean;
  etag?: string;
  lastModified?: string | Date;
  AccessLevel?: string;
  KeywordAISuggestion?: string;
  DataSource?: string;
  SuggestedAICategories?: string;
  SummaryOfSuggestedAI?: string;
}

export const addDocuments = async (documents: Document[], indexName_input?: string) => {
  try {
    if (!documents || documents.length <= 0) {
      return;
    }
    const index = client.index(indexName_input || indexName);
    const response = await index.addDocuments(documents, { primaryKey: 'id' });

    return response;
  } catch (error) {
    console.error('Error adding documents:', error);
  }
};

export const getSearchFilters = (
  userId: string,
  groups: string[],
  chatFlows?: string[],
  userIds?: string[],
) => {
  const groupFilters = groups.length
    ? `allowedGroups IN [${groups.map((group) => `${group}`).join(', ')}]`
    : null;

  const chatFlowFilters = chatFlows?.length
    ? `allowedChatFlows IN [${chatFlows.map((chatFlow) => `${chatFlow}`).join(', ')}]`
    : null;

  const userIdFilters = userIds?.length
    ? `allowedUserIds IN [${userIds.map((id) => `${id}`).join(', ')}]`
    : null;

  const visibilityFilter = `visibility = public OR visibility NOT EXISTS`;
  const privateFilters = [groupFilters, chatFlowFilters, userIdFilters];
  const userFilter = userId ? `userId = ${userId}` : null;

  const filterParts = [visibilityFilter, ...privateFilters, userFilter].filter(Boolean);
  const filter = filterParts.join(' OR ');
  return filter;
};

export const basicSearch = async (
  query: string,
  rootPrefix: string,
  limit: number,
  offset: number,
  userId: string,
  groups: string[],
  chatFlows?: string[],
  userIds?: string[],
) => {
  try {
    // const filter = getSearchFilters(userId, groups, chatFlows, userIds);

    const index = client.index(indexName);
    let result;
    const response = await index.search(rootPrefix + query, {
      q: rootPrefix + query,
      limit,
      offset,
      // filter,
    });
    if (response.hits.length <= 0) {
      return [];
    } else {
      const hits = response.hits.map((doc) => ({
        key: doc.key,
        name: doc.name,
        size: doc.size,
        etag: doc.etag,
        lastModified: doc.lastModified,
        contentType: doc.idFolder ? '' : getContentType(doc.name),
        idFolder: doc.idFolder,
        userId: doc.userId,
        visibility: doc.visibility,
        allowedGroups: doc.allowedGroups,
        allowedChatFlows: doc.allowedChatFlows,
      }));
      result = hits;
    }
    return { result, estimatedTotalHits: response.estimatedTotalHits };
  } catch (error) {
    console.error('Error performing basic search:', error);
  }
};

export const basicSearchAll = async (
  query: string,
  rootPrefix: string,
  limit: number = 200,
  offset: number = 0,
  userId: string,
  groups: string[],
  chatFlows?: string[],
) => {
  try {
    let allResults: any[] = [];
    let hasMore = true;
    while (hasMore) {
      const searchResponse: any = await basicSearch(
        query,
        rootPrefix,
        limit,
        offset,
        userId,
        groups,
        chatFlows,
      );
      const result = searchResponse?.result || [];
      allResults = [...allResults, ...result];

      if (result.length < limit) {
        hasMore = false;
      } else {
        offset += limit;
      }
    }

    return allResults;
  } catch (error) {
    console.error('Error performing basic search all:', error);
    return [];
  }
};

export const customSearchWithFilters = async (query: string, options?: SearchParams) => {
  try {
    const index = client.index(indexName);
    const response = await index.search(query, options);
    return response;
  } catch (error) {
    console.error('Error performing custom search with filters:', error);
  }
};

export const initIndexSync = async () => {
  try {
    const existingIndex = await client.getIndex(`${indexName}_synch`).catch(() => null);
    if (existingIndex) {
      await client.deleteIndex(`${indexName}_synch`);
      await createIndex(`${indexName}_synch`);
    } else {
      await createIndex(`${indexName}_synch`);
    }
  } catch (error) {
    console.error('Error initializing index synchronization:', error);
  }
};

export const swapIndexes = async (index1 = indexName, index2 = indexSync) => {
  try {
    const tempIndexName = await client.swapIndexes([{ indexes: [index1, index2] }]);
    console.log('🚀 ~ swapIndexes ~ tempIndexName:', tempIndexName);
  } catch (error) {
    console.error('Error swapping indexes:', error);
  }
};

export const setupMeilisearch = async () => {
  try {
    const index = client.index(indexName);
    const currentAttributes = await index.getFilterableAttributes();

    const sortedCurrent = [...currentAttributes].sort();
    const sortedDesired = [...desiredFilterableAttributes].sort();

    const needUpdate = JSON.stringify(sortedCurrent) !== JSON.stringify(sortedDesired);
    if (needUpdate) {
      await index.updateFilterableAttributes(desiredFilterableAttributes);
    }
  } catch (error) {
    console.error('Error setup meilisearch:', error);
  }
};
