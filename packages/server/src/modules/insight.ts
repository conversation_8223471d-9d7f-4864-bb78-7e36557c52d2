import { BedrockRuntimeClient, InvokeModelCommand } from '@aws-sdk/client-bedrock-runtime';
import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { NodeHttpHandler } from '@smithy/node-http-handler';
import axios from 'axios';
import FormData from 'form-data';
import { v4 as uuidv4 } from 'uuid';

const taskDescription = `
Bạn là một agent hỗ trợ xử lý văn bản hành chính, có nhiệm vụ đề xuất cacs câu hỏi gợi ý giúp người dùng hiểu rõ và khai thác văn bản hiệu quả.

## Mục tiêu
Mỗi câu hỏi nên có một trong hai mục tiêu sau:
- Gợi ý hành động hoặc bước tiếp theo cần thực hiện dựa trên nội dung văn bản
- Làm rõ thông tin chi tiết quan trọng đã có trong bản tóm tắt

## Đầu vào
\`\`\`
Nội dung văn bản:
[Nội dung gốc của văn bản hành chính.]
Bản tóm tắt
[Đoạn tóm tắt đã được người dùng cung cấp, phản ánh đầy đủ và chính xác nội dung quan trọng của văn bản.]
\`\`\`

## Hướng dẫn
1. **Đề xuất câu hỏi**, sử dụng tiêu chí phù hợp để đề xuất câu hỏi.

2. **Tuyệt đối chỉ sử dụng thông tin từ bản tóm tắt để hình thành các câu hỏi**.
   - Không được trích xuất, suy đoán hoặc bổ sung thông tin từ văn bản gốc nếu không xuất hiện trong bản tóm tắt.
   - Câu hỏi phải rõ ràng, liên quan đến nội dung chính, mục tiêu, đối tượng, đơn vị, pháp lý... nếu có.
   - Câu hỏi phải ngắn gọn, rõ ràng, xoay quanh thông tin quan trọng đã có

## Tiêu chí
- Mục đích chính của văn bản (văn bản này nhằm làm gì?).
- Đối tượng hoặc nội dung được đề cập (ví dụ: tài sản, sự kiện, nghĩa vụ...).
- Cơ quan ban hành và đơn vị được đề nghị xử lý.
- Căn cứ pháp lý được trích dẫn.
- Hành động được đề nghị thực hiện hoặc cơ quan cần giải quyết.

## Cấu trúc phản hồi
Trả về kết quả là một danh sách (array list) gồm 5 câu hỏi dạng chuỗi. Không trả về định dạng JSON object, không đưa thêm bất kỳ thông tin nào khác.

Ví dụ:
["Câu hỏi 1", "Câu hỏi 2", "Câu hỏi 3", "Câu hỏi 4", "Câu hỏi 5"]

## Lưu ý:
- Tất cả các câu hỏi gợi ý phải **chỉ sử dụng thông tin có trong bản tóm tắt nội dung văn bản**. Tuyệt đối không được suy đoán hoặc tạo thêm thông tin không có trong bản tóm tắt.`;

const bedrockClient = new BedrockRuntimeClient({
  region: process.env.S3_STORAGE_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.S3_STORAGE_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.S3_STORAGE_SECRET_ACCESS_KEY || '',
  },
});

export const s3Client = new S3Client({
  credentials: {
    accessKeyId: process.env.S3_STORAGE_ACCESS_KEY_ID! || process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey:
      process.env.S3_STORAGE_SECRET_ACCESS_KEY! || process.env.AWS_SECRET_ACCESS_KEY!,
  },
  region: process.env.S3_STORAGE_REGION || 'us-east-1',
  requestHandler: new NodeHttpHandler({
    connectionTimeout: 600000,
  }),
});
export const BUCKET_NAME = process.env.S3_STORAGE_BUCKET_NAME || 'unknown';

const ATTACHMENT_INSIGHT_URL =
  'https://stock.cmcts.ai/c-agent/api/v1/attachments/6ef3abbc-bae7-4405-9fa9-8398b88b9129/';

const PREDICTION_Insight_URL =
  'https://stock.cmcts.ai/c-agent/api/v1/prediction/6ef3abbc-bae7-4405-9fa9-8398b88b9129';
const PREDICTION_Report_URL =
  'https://stock.cmcts.ai/c-agent/api/v1/prediction/2e546f0e-bf1e-491c-ac6c-862cd0af409d';

const ATTACHMENT_SUMMARY_URL =
  'https://stock.cmcts.ai/c-agent/api/v1/attachments/73b71ecf-545f-4421-b942-fe606180ba2d/';
const PREDICTION_Summary_URL =
  'https://stock.cmcts.ai/c-agent/api/v1/prediction/73b71ecf-545f-4421-b942-fe606180ba2d';

type UploadResponse = {
  file_name: string;
  response: any;
};

export async function uploadFile(
  files: Buffer[] | File[],
  chatId: string,
  url: string,
): Promise<any> {
  try {
    const formData = new FormData();
    formData.append('chatId', chatId);

    for (let i = 0; i < files.length; i++) {
      const file: any = files[i];

      if (file instanceof Buffer) {
        // Handle Buffer
        const fileName = `file-${i}.bin`;
        formData.append('files', file, { filename: fileName });
      } else {
        // Handle File
        const fileName = file.name || `file-${i}`;
        const fileBuffer = Buffer.from(await file.arrayBuffer());
        formData.append('files', fileBuffer, fileName);
      }
    }

    const response = await axios.post(url + chatId, formData, {
      headers: { ...formData.getHeaders() },
    });

    return response;
  } catch (error: any) {
    throw error;
  }
}

export async function queryAPI(payload: any, url: string): Promise<any> {
  try {
    const response = await axios.post(url, payload);
    return response.data;
  } catch (error: any) {
    console.error('Error querying API:', error.message);
    return null;
  }
}

export async function processFilesIndividually(
  files: File[],
  question: string = '.',
  ATTACHMENT_INSIGHT_URL_INPUT = '',
  PREDICTION_Insight_URL_INPUT = '',
) {
  try {
    const chatId = uuidv4();

    const attachmentResponse = await uploadFile(
      files,
      chatId,
      ATTACHMENT_INSIGHT_URL_INPUT || ATTACHMENT_INSIGHT_URL,
    );

    if (!attachmentResponse) {
      throw new Error('Failed to upload files');
    }

    const uploads = files.map((file, index) => ({
      data: attachmentResponse.data[index].content,
      type: 'file:full',
      name: file.name,
      mime: file.type,
    }));

    if (uploads[0].data) {
      const payload = {
        question,
        chatId,
        uploads,
      };
      const text = await queryAPI(payload, PREDICTION_Insight_URL_INPUT || PREDICTION_Insight_URL);
      return { context: text, contentFile: attachmentResponse.data[0].content };
    } else {
      throw new Error('No data found in uploads');
    }
  } catch (error: any) {
    throw error;
  }
}

export async function predictionReport(question: string = '.'): Promise<void> {
  try {
    const chatId = uuidv4();
    const payload = {
      question,
      chatId,
    };
    const result_report = await queryAPI(payload, PREDICTION_Report_URL);

    if (result_report?.text === '' || !result_report?.text) {
      throw new Error('Failed to generate report');
    }

    const now = new Date();
    const options = { timeZone: 'Asia/Bangkok', hour12: false };
    const formatter = new Intl.DateTimeFormat('en-GB', {
      ...options,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
    const parts = formatter.formatToParts(now);
    const formattedTime = `[${parts[6].value}:${parts[8].value}]-${parts[0].value}-${parts[2].value}-${parts[4].value}`;

    const reportContent: string =
      (result_report?.text as string).split('Final Answer:').pop() || '';

    const fileContent = Buffer.from(reportContent, 'utf-8');
    const fileNameReport = `report_${formattedTime}.txt`;
    const uploadParams = {
      Bucket: BUCKET_NAME,
      Key: `BKTTW/reports/${formattedTime}/${fileNameReport}`,
      Body: fileContent,
    };

    await s3Client.send(new PutObjectCommand(uploadParams));

    const blob = new Blob([reportContent], { type: 'text/plain' });
    const file = new File([blob], fileNameReport, { type: 'text/plain' });

    const attachmentResponse = await uploadFile([file], chatId, ATTACHMENT_SUMMARY_URL);
    if (!attachmentResponse) {
      throw new Error('Failed to upload files');
    }

    const uploads = [
      {
        data: attachmentResponse.data[0]?.content,
        type: 'file:full',
        name: fileNameReport,
        mime: 'text/plain',
      },
    ];

    const chatId_summary = uuidv4();

    const payload_summary = {
      question,
      chatId: chatId_summary,
      uploads,
    };

    const result_summary = await queryAPI(payload_summary, PREDICTION_Summary_URL);

    const summaryContent: string =
      (result_summary?.text as string).split('Final Answer:').pop() || '';

    const fileContentSummary = Buffer.from(summaryContent, 'utf-8');
    const fileNameSummary = `summary_${formattedTime}.txt`;

    const uploadParams_Summary = {
      Bucket: BUCKET_NAME,
      Key: `BKTTW/reports/${formattedTime}/${fileNameSummary}`,
      Body: fileContentSummary,
    };

    await s3Client.send(new PutObjectCommand(uploadParams_Summary));
  } catch (error: any) {
    console.error(`Error processing file:`, error.message);
  }
}

export async function uploadTextToS3(
  contentText: string,
  summaryText: string,
  fileName: string,
  id_file: string,
): Promise<void> {
  try {
    const fileSummaryText = Buffer.from(summaryText, 'utf-8');
    const fileContent = Buffer.from(contentText, 'utf-8');

    const uploadSummary = {
      Bucket: BUCKET_NAME,
      Key: `C_Office/summary/${id_file}/${fileName}.txt`,
      Body: fileSummaryText,
    };

    const uploadContent = {
      Bucket: BUCKET_NAME,
      Key: `C_Office/content/${id_file}/${fileName}.txt`,
      Body: fileContent,
    };

    await s3Client.send(new PutObjectCommand(uploadSummary));
    await s3Client.send(new PutObjectCommand(uploadContent));

    console.log(`File "${fileName}.txt" uploaded to S3 bucket "${BUCKET_NAME}"`);
  } catch (error: any) {
    console.error('Error uploading summaryText to S3:', error);
    throw error;
  }
}

export const analyze = async (
  content: string,
  summary: string,
  keyToSaveFile: string = '',
): Promise<string> => {
  try {
    const formattedPrompt = taskDescription
      .replace('[Nội dung gốc của văn bản hành chính.]', content)
      .replace(
        '[Đoạn tóm tắt đã được người dùng cung cấp, phản ánh đầy đủ và chính xác nội dung quan trọng của văn bản.]',
        summary,
      );
    console.log('🚀 ~ insight.ts:304 ~ formattedPrompt:', formattedPrompt);

    const command = new InvokeModelCommand({
      modelId: 'us.anthropic.claude-3-7-sonnet-20250219-v1:0',
      contentType: 'application/json',
      accept: 'application/json',
      body: JSON.stringify({
        anthropic_version: 'bedrock-2023-05-31',
        max_tokens: 100000,
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: formattedPrompt,
              },
            ],
          },
        ],
      }),
    });

    const response = await bedrockClient.send(command);
    const responseBody = JSON.parse(new TextDecoder().decode(response.body));
    const result = responseBody.content[0].text as string;

    if (keyToSaveFile) {
      const fileContent = Buffer.from(result, 'utf-8');
      const uploadParams = {
        Bucket: BUCKET_NAME,
        Key: keyToSaveFile,
        Body: fileContent,
      };
      await s3Client.send(new PutObjectCommand(uploadParams));
    }

    return result;
  } catch (error) {
    throw error;
  }
};
