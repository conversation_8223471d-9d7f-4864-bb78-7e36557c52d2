import { add, get } from './postgres';
import type {
  AuditAction,
  AuditLogEntry,
  AuditLogFilter,
  AuditLogOptions,
  AuditLogQueryResult,
  AuditTargetType,
  ChangeSet,
  CreateAuditLogParams,
} from '../types/audit';

export class AuditLogger {
  private static instance: AuditLogger;

  private constructor() {}

  public static getInstance(): AuditLogger {
    if (!AuditLogger.instance) {
      AuditLogger.instance = new AuditLogger();
    }
    return AuditLogger.instance;
  }

  async logCreate(
    target_type: AuditTargetType,
    target_id: string,
    target_name: string,
    data: Record<string, any>,
    options: AuditLogOptions,
  ): Promise<void> {
    try {
      const changes: ChangeSet = {
        after: data,
        fields_changed: Object.keys(data),
      };

      await this.createAuditLog({
        action: 'CREATE',
        target_type,
        target_id,
        target_name,
        options,
        changes,
        success: true,
      });
    } catch (error) {
      console.error('Error logging CREATE operation:', error);
      await this.logError('CREATE', target_type, target_id, target_name, options, error);
    }
  }

  async logRead(
    target_type: AuditTargetType,
    target_id: string,
    target_name: string,
    options: AuditLogOptions,
    query_params?: Record<string, any>,
  ): Promise<void> {
    try {
      const metadata = {
        ...options.metadata,
        query_params,
      };

      await this.createAuditLog({
        action: 'READ',
        target_type,
        target_id,
        target_name,
        options: { ...options, metadata },
        success: true,
      });
    } catch (error) {
      console.error('Error logging READ operation:', error);
      await this.logError('READ', target_type, target_id, target_name, options, error);
    }
  }

  async logUpdate(
    target_type: AuditTargetType,
    target_id: string,
    target_name: string,
    before_data: Record<string, any>,
    after_data: Record<string, any>,
    options: AuditLogOptions,
  ): Promise<void> {
    try {
      const changes = this.calculateChanges(before_data, after_data);

      await this.createAuditLog({
        action: 'UPDATE',
        target_type,
        target_id,
        target_name,
        options,
        changes,
        success: true,
      });
    } catch (error) {
      console.error('Error logging UPDATE operation:', error);
      await this.logError('UPDATE', target_type, target_id, target_name, options, error);
    }
  }

  async logDelete(
    target_type: AuditTargetType,
    target_id: string,
    target_name: string,
    deleted_data: Record<string, any>,
    options: AuditLogOptions,
  ): Promise<void> {
    try {
      const changes: ChangeSet = {
        before: deleted_data,
        fields_changed: Object.keys(deleted_data),
      };

      await this.createAuditLog({
        action: 'DELETE',
        target_type,
        target_id,
        target_name,
        options,
        changes,
        success: true,
      });
    } catch (error) {
      console.error('Error logging DELETE operation:', error);
      await this.logError('DELETE', target_type, target_id, target_name, options, error);
    }
  }

  private async logError(
    action: AuditAction,
    target_type: AuditTargetType,
    target_id: string,
    target_name: string,
    options: AuditLogOptions,
    error: any,
  ): Promise<void> {
    try {
      await this.createAuditLog({
        action,
        target_type,
        target_id,
        target_name,
        options,
        error_message: error?.message || String(error),
        success: false,
      });
    } catch (logError) {
      console.error('Failed to log error to audit table:', logError);
    }
  }

  private async createAuditLog(params: CreateAuditLogParams): Promise<void> {
    const {
      action,
      target_type,
      target_id,
      target_name,
      options,
      changes,
      error_message,
      success = true,
    } = params;

    const query = `
      INSERT INTO audit_logs (
        action, target_type, target_id, target_name, username, user_id,
        changes, metadata, ip_address, user_agent, session_id,
        error_message, success
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
    `;

    const values = [
      action,
      target_type,
      target_id,
      target_name,
      options.username,
      options.user_id,
      changes ? JSON.stringify(changes) : null,
      options.metadata ? JSON.stringify(options.metadata) : null,
      options.ip_address,
      options.user_agent,
      options.session_id,
      error_message,
      success,
    ];

    await add(query, values);
  }

  private calculateChanges(before: Record<string, any>, after: Record<string, any>): ChangeSet {
    const fields_changed: string[] = [];
    const changes: ChangeSet = {
      before: {},
      after: {},
      fields_changed: [],
    };

    const allKeys = new Set([...Object.keys(before), ...Object.keys(after)]);

    for (const key of Array.from(allKeys)) {
      const beforeValue = before[key];
      const afterValue = after[key];

      if (!this.isEqual(beforeValue, afterValue)) {
        fields_changed.push(key);
        changes.before![key] = beforeValue;
        changes.after![key] = afterValue;
      }
    }

    changes.fields_changed = fields_changed;
    return changes;
  }

  private isEqual(a: any, b: any): boolean {
    if (a === b) return true;
    if (a == null || b == null) return a === b;
    if (typeof a !== typeof b) return false;
    if (typeof a === 'object') {
      return JSON.stringify(a) === JSON.stringify(b);
    }
    return false;
  }

  async getAuditLogs(filter: AuditLogFilter = {}): Promise<AuditLogQueryResult> {
    try {
      const {
        action,
        target_type,
        target_id,
        username,
        user_id,
        start_date,
        end_date,
        success,
        limit = 50,
        offset = 0,
      } = filter;

      let whereConditions: string[] = [];
      let values: any[] = [];
      let paramIndex = 1;

      if (action) {
        whereConditions.push(`action = $${paramIndex++}`);
        values.push(action);
      }

      if (target_type) {
        whereConditions.push(`target_type = $${paramIndex++}`);
        values.push(target_type);
      }

      if (target_id) {
        whereConditions.push(`target_id = $${paramIndex++}`);
        values.push(target_id);
      }

      if (username) {
        whereConditions.push(`username = $${paramIndex++}`);
        values.push(username);
      }

      if (user_id) {
        whereConditions.push(`user_id = $${paramIndex++}`);
        values.push(user_id);
      }

      if (start_date) {
        whereConditions.push(`timestamp >= $${paramIndex++}`);
        values.push(start_date);
      }

      if (end_date) {
        whereConditions.push(`timestamp <= $${paramIndex++}`);
        values.push(end_date);
      }

      if (success !== undefined) {
        whereConditions.push(`success = $${paramIndex++}`);
        values.push(success);
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      const countQuery = `SELECT COUNT(*) FROM audit_logs ${whereClause}`;
      const countResult = await get(countQuery, values);
      const total_count = parseInt(countResult[0].count);

      const dataQuery = `
        SELECT * FROM audit_logs 
        ${whereClause}
        ORDER BY timestamp DESC 
        LIMIT $${paramIndex++} OFFSET $${paramIndex++}
      `;
      values.push(limit, offset);

      const logs = await get(dataQuery, values);

      const page = Math.floor(offset / limit) + 1;
      const total_pages = Math.ceil(total_count / limit);

      return {
        logs,
        total_count,
        page,
        limit,
        total_pages,
      };
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      throw error;
    }
  }
}

export const auditLogger = AuditLogger.getInstance();

export const logCreate = (
  target_type: AuditTargetType,
  target_id: string,
  target_name: string,
  data: Record<string, any>,
  options: AuditLogOptions,
) => auditLogger.logCreate(target_type, target_id, target_name, data, options);

export const logRead = (
  target_type: AuditTargetType,
  target_id: string,
  target_name: string,
  options: AuditLogOptions,
  query_params?: Record<string, any>,
) => auditLogger.logRead(target_type, target_id, target_name, options, query_params);

export const logUpdate = (
  target_type: AuditTargetType,
  target_id: string,
  target_name: string,
  before_data: Record<string, any>,
  after_data: Record<string, any>,
  options: AuditLogOptions,
) => auditLogger.logUpdate(target_type, target_id, target_name, before_data, after_data, options);

export const logDelete = (
  target_type: AuditTargetType,
  target_id: string,
  target_name: string,
  deleted_data: Record<string, any>,
  options: AuditLogOptions,
) => auditLogger.logDelete(target_type, target_id, target_name, deleted_data, options);

export const getAuditLogs = (filter?: AuditLogFilter) => auditLogger.getAuditLogs(filter);
