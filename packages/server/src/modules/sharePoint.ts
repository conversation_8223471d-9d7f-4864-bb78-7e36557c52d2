import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { NodeHttpHandler } from '@smithy/node-http-handler';
import axios from 'axios';
import crypto from 'crypto';
import dotenv from 'dotenv';
import * as mime from 'mime-types';

import { add, exists, get, update } from './postgres';
import { createFolder, deleteObjects, renameObject, uploadMultipleFiles } from './s3';

dotenv.config();

export const s3Client = new S3Client({
  credentials: {
    accessKeyId: process.env.S3_STORAGE_ACCESS_KEY_ID!,
    secretAccessKey: process.env.S3_STORAGE_SECRET_ACCESS_KEY!,
  },
  region: process.env.S3_STORAGE_REGION || 'us-east-1',
  requestHandler: new NodeHttpHandler({
    connectionTimeout: 600000,
  }),
});

export const BUCKET_NAME = process.env.S3_STORAGE_BUCKET_NAME || 'unknown';

const SHAREPOINT_CLIENT_ID = process.env.SHAREPOINT_CLIENT_ID!;
const SHAREPOINT_CLIENT_SECRET = process.env.SHAREPOINT_CLIENT_SECRET!;
const SHAREPOINT_REDIRECT_URI = process.env.SHAREPOINT_REDIRECT_URI!;
const SHAREPOINT_TENANT_ID = process.env.SHAREPOINT_TENANT_ID!;

export async function getAllSites(accessToken: string) {
  const url = 'https://graph.microsoft.com/v1.0/sites?search=*';

  try {
    const response = await axios.get(url, {
      headers: { Authorization: `Bearer ${accessToken}` },
    });

    const sites = response.data.value;

    const enrichedSites = sites.map((site: any) => {
      const hostname = new URL(site.webUrl).hostname;
      const sitePath = new URL(site.webUrl).pathname.replace(/^\/+/, '');
      return {
        ...site,
        apiUrl: `https://graph.microsoft.com/v1.0/sites/${hostname}:/${sitePath}:/drive`,
      };
    });

    return enrichedSites;
  } catch (error) {
    console.error('Error fetching sites:', error);
    throw new Error('Failed to fetch sites');
  }
}

export async function getSharePointSiteId(accessToken: string, hostname: string, sitePath: string) {
  const url = `https://graph.microsoft.com/v1.0/sites/${hostname}:/${sitePath}`;
  const res = await fetch(url, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });

  if (!res.ok) {
    throw new Error(`Failed to get SharePoint site ID: ${res.status} ${await res.text()}`);
  }

  const data = await res.json();
  return {
    siteId: data.id,
    apiUrl: `https://graph.microsoft.com/v1.0/sites/${data.id}/drive`,
  };
}

export function getAuthUrl(): string {
  return `https://login.microsoftonline.com/${SHAREPOINT_TENANT_ID}/oauth2/v2.0/authorize?client_id=${SHAREPOINT_CLIENT_ID}&response_type=code&redirect_uri=${SHAREPOINT_REDIRECT_URI}&scope=offline_access Files.Read.All Sites.Read.All`;
}

export async function saveRefreshTokenToDB(userId: string, refreshToken: string): Promise<void> {
  const tokenExists = await exists('SELECT 1 FROM user_tokens WHERE user_id = $1 AND type = $2', [
    userId,
    'SharePoint',
  ]);

  if (tokenExists) {
    await update(
      'UPDATE user_tokens SET refresh_token = $2, updated_at = CURRENT_TIMESTAMP WHERE user_id = $1 AND type = $3',
      [userId, refreshToken, 'SharePoint'],
    );
  } else {
    await add('INSERT INTO user_tokens (user_id, refresh_token, type) VALUES ($1, $2, $3)', [
      userId,
      refreshToken,
      'SharePoint',
    ]);
  }
}

export async function getRefreshTokenFromDB(userId: string): Promise<string> {
  const query = 'SELECT refresh_token FROM user_tokens WHERE user_id = $1 AND type = $2';
  const result = await get(query, ['1', 'SharePoint']);

  if (!result.length) {
    throw new Error('SharePoint refresh token not found for user');
  }

  return result[0].refresh_token;
}

export async function getSharePointRefreshToken(
  authorizationCode: string,
  userId: string,
): Promise<string> {
  const tokenEndpoint = `https://login.microsoftonline.com/${SHAREPOINT_TENANT_ID}/oauth2/v2.0/token`;

  const params = new URLSearchParams({
    client_id: SHAREPOINT_CLIENT_ID,
    client_secret: SHAREPOINT_CLIENT_SECRET,
    code: authorizationCode,
    grant_type: 'authorization_code',
    redirect_uri: SHAREPOINT_REDIRECT_URI,
    scope: 'offline_access Files.Read.All Sites.Read.All',
  });

  try {
    const response = await axios.post(tokenEndpoint, params);
    const refreshToken = response.data.refresh_token;

    await saveRefreshTokenToDB(userId, refreshToken);

    return refreshToken;
  } catch (error: any) {
    console.error(
      '❌ Error getting SharePoint refresh token:',
      error.response?.data || error.message,
    );
    throw new Error('Failed to get SharePoint refresh token');
  }
}

export async function getSharePointAccessToken(refreshToken: string): Promise<string> {
  const tokenEndpoint = `https://login.microsoftonline.com/${SHAREPOINT_TENANT_ID}/oauth2/v2.0/token`;

  const params = new URLSearchParams({
    client_id: SHAREPOINT_CLIENT_ID,
    client_secret: SHAREPOINT_CLIENT_SECRET,
    refresh_token: refreshToken,
    grant_type: 'refresh_token',
    scope: 'offline_access Files.Read.All Sites.Read.All',
  });

  try {
    const response = await axios.post(tokenEndpoint, params);
    return response.data.access_token;
  } catch (error: any) {
    console.error(
      '❌ Error getting SharePoint access token:',
      error.response?.data || error.message,
    );
    throw new Error('Failed to get SharePoint access token');
  }
}

export async function getSharePointFiles(
  accessToken: string,
  apiUrl: string,
  folderId: string = 'root',
): Promise<any[]> {
  const files: any[] = [];
  let nextUrl = `${apiUrl}/items/${folderId}/children`;

  while (nextUrl) {
    const response = await axios.get(nextUrl, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    files.push(...response.data.value);

    nextUrl = response.data['@odata.nextLink'] || null;
  }

  return files.map((file) => ({
    id: file.id,
    name: file.name,
    type: file.folder ? 'folder' : 'file',
    size: file.size,
    lastModified: file.lastModifiedDateTime,
    children: file.children || [],
  }));
}

export async function downloadSharePointFile(
  fileId: string,
  apiUrl: string,
  accessToken: string,
): Promise<{ buffer: Buffer | null; fileName: string; prefix: string }> {
  const url = `${apiUrl}/items/${fileId}/content`;
  const metadataUrl = `${apiUrl}/items/${fileId}`;

  try {
    const metadataResponse = await axios.get(metadataUrl, {
      headers: { Authorization: `Bearer ${accessToken}` },
    });

    const isFolder = metadataResponse.data.folder !== undefined;
    let fileName = 'untitled';
    let prefix = '';

    if (isFolder) {
      fileName = metadataResponse.data.name;
      const parentPath = metadataResponse.data.parentReference.path;

      if (parentPath?.includes(':/')) {
        const parts = parentPath.split(':/');
        prefix = parts[1] + '/' + fileName;
      }

      return { buffer: null, fileName, prefix };
    } else {
      const response = await axios.get(url, {
        responseType: 'arraybuffer',
        headers: { Authorization: `Bearer ${accessToken}` },
      });

      const disposition = response.headers['content-disposition'];
      if (disposition) {
        const filenameMatch = disposition.match(/filename\*?=(?:UTF-8'')?["']?([^"';\n\r]+)/i);
        if (filenameMatch && filenameMatch[1]) {
          try {
            fileName = decodeURIComponent(filenameMatch[1]);
          } catch {
            fileName = filenameMatch[1]; // fallback
          }
        }
      } else {
        fileName = metadataResponse.data.name;
      }

      const parentPath = metadataResponse.data.parentReference.path;
      if (parentPath?.includes(':/')) {
        const parts = parentPath.split(':/');
        prefix = parts[1] + '/' + fileName;
      }

      const buffer = Buffer.from(response.data);
      return { buffer, fileName, prefix };
    }
  } catch (error) {
    console.error(`❌ Error downloading file ${fileId} from SharePoint:`, error);
    throw new Error(`Failed to download file ${fileId}`);
  }
}

function calculateFileHash(buffer: Buffer): string {
  const hash = crypto.createHash('sha256');
  hash.update(buffer);
  return hash.digest('hex');
}

async function getSharePointItem(accessToken: string, apiUrl: string, itemId: string) {
  const res = await fetch(`${apiUrl}/items/${itemId}`, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return await res.json();
}

export async function syncSharePointFolder(
  folderId: string | 'root',
  accessToken: string,
  apiUrl: string,
  parentPrefix = '',
): Promise<string[]> {
  console.log(`⏳ Syncing SharePoint folder: ${folderId} with parentPrefix: ${parentPrefix}`);

  const folderItems = await getSharePointFiles(accessToken, apiUrl, folderId);
  const uploadedKeys: string[] = [];
  if (folderItems.length === 0) {
    console.log(`📁 Thư mục rỗng: ${parentPrefix} (folderId: ${folderId}), tạo folder trên S3`);
    await createFolder(parentPrefix);

    const currentName = parentPrefix.split('/').filter(Boolean).pop() || '';
    const folderPrefix = parentPrefix;
    const folderKey = parentPrefix.endsWith('/') ? parentPrefix : `${parentPrefix}/`;

    await add(
      `INSERT INTO sharepoint_synced_items (item_id, prefix, file_name, updated_at)
             VALUES ($1, $2, $3, $4)`,
      [folderId, folderPrefix, currentName, new Date().toISOString()],
    );
    return [folderKey];
  }

  if (folderId !== 'root') {
    console.log(`🔍 Checking folder: ${folderId}`);

    const currentFolder = await getSharePointItem(accessToken, apiUrl, folderId);
    const currentPrefixParts = parentPrefix.split('/');
    const currentName = currentPrefixParts.filter(Boolean).pop() || '';
    const folderPrefix = parentPrefix.slice(0, -`${currentName}/`.length);

    console.log(`🔄 Fetching existing record for folder: ${folderId} with prefix: ${folderPrefix}`);
    const [existingRecord] = await get(
      `SELECT * FROM sharepoint_synced_items WHERE item_id = $1 AND prefix = $2`,
      [folderId, folderPrefix],
    );

    if (!existingRecord) {
      await add(
        `INSERT INTO sharepoint_synced_items (item_id, prefix, file_name, updated_at)
                 VALUES ($1, $2, $3, $4)`,
        [
          folderId,
          folderPrefix,
          currentName,
          currentFolder.lastModifiedDateTime || new Date().toISOString(),
        ],
      );
    } else if (existingRecord.file_name !== currentName) {
      const oldPrefix = `${folderPrefix}${existingRecord.file_name}/`;
      const newPrefix = `${folderPrefix}${currentName}/`;

      await renameObject(oldPrefix, newPrefix);

      await update(
        `UPDATE sharepoint_synced_items SET file_name = $1, updated_at = $2 WHERE item_id = $3 AND prefix = $4`,
        [currentName, new Date().toISOString(), folderId, folderPrefix],
      );

      await update(
        `UPDATE sharepoint_synced_items SET prefix = regexp_replace(prefix, $1, $2) WHERE prefix LIKE $3`,
        [oldPrefix, newPrefix, `${oldPrefix}%`],
      );

      uploadedKeys.push(newPrefix);
    }
  }

  await Promise.allSettled(
    folderItems.map(async (item) => {
      try {
        const itemPath = `${parentPrefix}${item.name}`;
        const isFolder = item.type === 'folder';

        const [existingRecord] = await get(
          `SELECT * FROM sharepoint_synced_items WHERE item_id = $1 AND prefix = $2`,
          [item.id, parentPrefix],
        );

        if (isFolder) {
          const folderKey = `${itemPath}/`;
          const metadataKey = `${folderKey}.metadata.json`;
          const metadataContent = {
            metadataAttributes: { DataSource: 'SharePoint' },
          };

          await s3Client.send(
            new PutObjectCommand({
              Bucket: BUCKET_NAME,
              Key: metadataKey,
              Body: JSON.stringify(metadataContent, null, 2),
              ContentType: 'application/json',
            }),
          );

          if (!existingRecord) {
            await add(
              `INSERT INTO sharepoint_synced_items (item_id, prefix, file_name, updated_at)
             VALUES ($1, $2, $3, $4)`,
              [
                item.id,
                parentPrefix,
                item.name,
                item.lastModifiedDateTime || new Date().toISOString(),
              ],
            );
          } else if (existingRecord.file_name !== item.name) {
            const oldPrefix = `${parentPrefix}${existingRecord.file_name}/`;
            const newPrefix = folderKey;

            await renameObject(oldPrefix, newPrefix);

            await update(
              `UPDATE sharepoint_synced_items SET file_name = $1, updated_at = $2 WHERE item_id = $3 AND prefix = $4`,
              [item.name, new Date().toISOString(), item.id, parentPrefix],
            );

            await update(
              `UPDATE sharepoint_synced_items SET prefix = regexp_replace(prefix, $1, $2) WHERE prefix LIKE $3`,
              [oldPrefix, newPrefix, `${oldPrefix}%`],
            );

            uploadedKeys.push(newPrefix);
          }

          const subKeys = await syncSharePointFolder(
            item.id,
            accessToken,
            apiUrl,
            `${parentPrefix}${item.name}/`,
          );
          uploadedKeys.push(...subKeys);
        } else {
          const { buffer, fileName } = await downloadSharePointFile(item.id, apiUrl, accessToken);
          if (!buffer) return;

          const newHash = calculateFileHash(buffer);
          const newPath = `${parentPrefix}${fileName}`;

          if (!existingRecord) {
            const uploaded = await uploadMultipleFiles([
              {
                prefix: parentPrefix,
                name: fileName,
                file: buffer,
                contentType: mime.lookup(fileName) || 'application/octet-stream',
              },
            ]);

            await add(
              `INSERT INTO sharepoint_synced_items (item_id, prefix, file_name, hash, updated_at)
             VALUES ($1, $2, $3, $4, $5)`,
              [
                item.id,
                parentPrefix,
                fileName,
                newHash,
                item.lastModifiedDateTime || new Date().toISOString(),
              ],
            );

            uploadedKeys.push(...uploaded);
          } else {
            const { file_name: oldName, hash: oldHash } = existingRecord;
            const isNameChanged = fileName !== oldName;
            const isContentChanged = newHash !== oldHash;
            const oldPath = `${parentPrefix}${oldName}`;

            if (isNameChanged && isContentChanged) {
              await deleteObjects([oldPath]);
              const uploaded = await uploadMultipleFiles([
                {
                  prefix: parentPrefix,
                  name: fileName,
                  file: buffer,
                  contentType: mime.lookup(fileName) || 'application/octet-stream',
                },
              ]);

              await update(
                `UPDATE sharepoint_synced_items SET file_name = $1, hash = $2, updated_at = $3 WHERE item_id = $4 AND prefix = $5`,
                [fileName, newHash, new Date().toISOString(), item.id, parentPrefix],
              );

              uploadedKeys.push(...uploaded);
            } else if (isNameChanged) {
              await renameObject(oldPath, newPath);
              await update(
                `UPDATE sharepoint_synced_items SET file_name = $1, updated_at = $2 WHERE item_id = $3 AND prefix = $4`,
                [fileName, new Date().toISOString(), item.id, parentPrefix],
              );
              uploadedKeys.push(newPath);
            } else if (isContentChanged) {
              const uploaded = await uploadMultipleFiles([
                {
                  prefix: parentPrefix,
                  name: fileName,
                  file: buffer,
                  contentType: mime.lookup(fileName) || 'application/octet-stream',
                },
              ]);

              await update(
                `UPDATE sharepoint_synced_items SET hash = $1, updated_at = $2 WHERE item_id = $3 AND prefix = $4`,
                [newHash, new Date().toISOString(), item.id, parentPrefix],
              );

              uploadedKeys.push(...uploaded);
            }
          }
        }
      } catch (err) {
        console.error(`❌ Lỗi khi xử lý item ${item.name}:`, err);
      }
    }),
  );

  console.log(`✅ Sync completed for folder: ${folderId} with parentPrefix: ${parentPrefix}`);
  return uploadedKeys;
}
