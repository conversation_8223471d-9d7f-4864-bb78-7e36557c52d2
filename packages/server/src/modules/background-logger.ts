import { Worker } from 'worker_threads';
import { pool } from './postgres';
import type { AuditLogEntry } from '../types/audit';

export type LogEntry = {
  id: string;
  entry: Omit<AuditLogEntry, 'id' | 'created_at' | 'updated_at'>;
  timestamp: number;
  retries: number;
};

class BackgroundLogger {
  private static instance: BackgroundLogger;
  private logQueue: LogEntry[] = [];
  private isProcessing = false;
  private maxRetries = 3;
  private batchSize = 10;
  private flushInterval = 1000; // 1 second
  private flushTimer?: NodeJS.Timeout;

  private constructor() {
    this.startFlushTimer();
    this.setupGracefulShutdown();
  }

  public static getInstance(): BackgroundLogger {
    if (!BackgroundLogger.instance) {
      BackgroundLogger.instance = new BackgroundLogger();
    }
    return BackgroundLogger.instance;
  }

  public async logAsync(entry: Omit<AuditLogEntry, 'id' | 'created_at' | 'updated_at'>): Promise<void> {
    const logEntry: LogEntry = {
      id: this.generateId(),
      entry,
      timestamp: Date.now(),
      retries: 0,
    };

    this.logQueue.push(logEntry);

    if (this.logQueue.length >= this.batchSize) {
      setImmediate(() => this.processQueue());
    }
  }

  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      if (this.logQueue.length > 0) {
        this.processQueue();
      }
    }, this.flushInterval);
  }

  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.logQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      const batch = this.logQueue.splice(0, this.batchSize);
      await this.processBatch(batch);
    } catch (error) {
      console.error('Error processing audit log batch:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  private async processBatch(batch: LogEntry[]): Promise<void> {
    const successfulEntries: string[] = [];
    const failedEntries: LogEntry[] = [];

    for (const logEntry of batch) {
      try {
        await this.insertAuditLog(logEntry.entry);
        successfulEntries.push(logEntry.id);
      } catch (error) {
        console.error(`Failed to insert audit log ${logEntry.id}:`, error);
        
        logEntry.retries++;
        if (logEntry.retries < this.maxRetries) {
          failedEntries.push(logEntry);
        } else {
          console.error(`Audit log ${logEntry.id} exceeded max retries, dropping`);
        }
      }
    }

    if (failedEntries.length > 0) {
      this.logQueue.unshift(...failedEntries);
    }

    if (successfulEntries.length > 0) {
      console.debug(`Successfully logged ${successfulEntries.length} audit entries`);
    }
  }

  private async insertAuditLog(entry: Omit<AuditLogEntry, 'id' | 'created_at' | 'updated_at'>): Promise<void> {
    const query = `
      INSERT INTO audit_logs (
        action, target_type, target_id, target_name, username, user_id,
        changes, metadata, ip_address, user_agent, session_id,
        error_message, success
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
    `;

    const values = [
      entry.action,
      entry.target_type,
      entry.target_id,
      entry.target_name,
      entry.username,
      entry.user_id,
      entry.changes ? JSON.stringify(entry.changes) : null,
      entry.metadata ? JSON.stringify(entry.metadata) : null,
      entry.ip_address,
      entry.user_agent,
      entry.session_id,
      entry.error_message,
      entry.success ?? true,
    ];

    await pool.query(query, values);
  }

  private generateId(): string {
    return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  public async flush(): Promise<void> {
    while (this.logQueue.length > 0) {
      await this.processQueue();
      if (this.logQueue.length > 0) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
  }

  public getQueueSize(): number {
    return this.logQueue.length;
  }

  public getStats(): { queueSize: number; isProcessing: boolean } {
    return {
      queueSize: this.logQueue.length,
      isProcessing: this.isProcessing,
    };
  }

  private setupGracefulShutdown(): void {
    const shutdown = async () => {
      console.log('Shutting down background logger...');
      if (this.flushTimer) {
        clearInterval(this.flushTimer);
      }
      await this.flush();
      console.log('Background logger shutdown complete');
    };

    process.on('SIGTERM', shutdown);
    process.on('SIGINT', shutdown);
    process.on('beforeExit', shutdown);
  }
}

export const backgroundLogger = BackgroundLogger.getInstance();

export async function logAuditEntryAsync(entry: Omit<AuditLogEntry, 'id' | 'created_at' | 'updated_at'>): Promise<void> {
  try {
    await backgroundLogger.logAsync(entry);
  } catch (error) {
    console.error('Failed to queue audit log entry:', error);
  }
}
