import { add, get, remove, update } from './postgres';
import { logCreate, logDelete, logRead, logUpdate } from './audit-logger';
import type { AuditLogOptions, AuditTargetType } from '../types/audit';

export class AuditWrapper {
  static async addWithAudit(
    query: string,
    values: any[],
    target_type: AuditTargetType,
    target_name: string,
    data: Record<string, any>,
    options: AuditLogOptions,
  ): Promise<any> {
    try {
      const result = await add(query, values);
      
      const target_id = result[0]?.id || values[0] || 'unknown';
      
      await logCreate(target_type, String(target_id), target_name, data, options);
      
      return result;
    } catch (error) {
      console.error('Error in addWithAudit:', error);
      throw error;
    }
  }

  static async getWithAudit(
    query: string,
    values: any[],
    target_type: AuditTargetType,
    target_id: string,
    target_name: string,
    options: AuditLogOptions,
    query_params?: Record<string, any>,
  ): Promise<any> {
    try {
      const result = await get(query, values);
      
      await logRead(target_type, target_id, target_name, options, query_params);
      
      return result;
    } catch (error) {
      console.error('Error in getWithAudit:', error);
      throw error;
    }
  }

  static async updateWithAudit(
    query: string,
    values: any[],
    target_type: AuditTargetType,
    target_id: string,
    target_name: string,
    before_data: Record<string, any>,
    after_data: Record<string, any>,
    options: AuditLogOptions,
  ): Promise<any> {
    try {
      const result = await update(query, values);
      
      await logUpdate(target_type, target_id, target_name, before_data, after_data, options);
      
      return result;
    } catch (error) {
      console.error('Error in updateWithAudit:', error);
      throw error;
    }
  }

  static async removeWithAudit(
    query: string,
    values: any[],
    target_type: AuditTargetType,
    target_id: string,
    target_name: string,
    deleted_data: Record<string, any>,
    options: AuditLogOptions,
  ): Promise<any> {
    try {
      const result = await remove(query, values);
      
      await logDelete(target_type, target_id, target_name, deleted_data, options);
      
      return result;
    } catch (error) {
      console.error('Error in removeWithAudit:', error);
      throw error;
    }
  }

  static async getBeforeUpdate(
    table: string,
    id_column: string,
    id_value: string,
  ): Promise<Record<string, any> | null> {
    try {
      const query = `SELECT * FROM ${table} WHERE ${id_column} = $1`;
      const result = await get(query, [id_value]);
      return result[0] || null;
    } catch (error) {
      console.error('Error getting before data:', error);
      return null;
    }
  }

  static extractUserInfo(req: any): Partial<AuditLogOptions> {
    return {
      ip_address: req?.headers?.['x-forwarded-for'] || req?.headers?.['x-real-ip'] || req?.ip,
      user_agent: req?.headers?.['user-agent'],
      session_id: req?.headers?.['x-session-id'] || req?.sessionID,
    };
  }

  static createAuditOptions(
    username: string,
    user_id?: string,
    req?: any,
    metadata?: Record<string, any>,
  ): AuditLogOptions {
    const userInfo = req ? this.extractUserInfo(req) : {};
    
    return {
      username,
      user_id,
      metadata,
      ...userInfo,
    };
  }
}

export const {
  addWithAudit,
  getWithAudit,
  updateWithAudit,
  removeWithAudit,
  getBeforeUpdate,
  extractUserInfo,
  createAuditOptions,
} = AuditWrapper;
