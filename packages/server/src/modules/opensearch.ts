import { Sha256 } from '@aws-crypto/sha256-js';
import { defaultProvider } from '@aws-sdk/credential-provider-node';
import { HttpRequest } from '@aws-sdk/protocol-http';
import { SignatureV4 } from '@aws-sdk/signature-v4';
import axios from 'axios';

const domainEndpoint =
  process.env.OPENSEARCH_ENDPOINT || 'https://mnpas7dxh5ijkq619j3d.us-east-1.aoss.amazonaws.com';

type CreateIndex = {
  acknowledged: boolean;
  shards_acknowledged: boolean;
  index: string;
};

async function getSignedRequest(method: string, path: string, body?: any) {
  const credentials = await defaultProvider()();
  const endpoint = new URL(domainEndpoint);

  const request = new HttpRequest({
    method,
    protocol: 'https:',
    path,
    headers: {
      'host': endpoint.host,
      'content-type': 'application/json',
    },
    body: body ? JSON.stringify(body) : undefined,
  });

  const signer = new SignatureV4({
    credentials,
    region: process.env.S3_STORAGE_REGION || process.env.AWS_REGION || 'us-east-1',
    service: 'aoss',
    sha256: Sha256,
  });

  const signedRequest = await signer.sign(request);
  return signedRequest;
}

async function createIndex(indexName: string): Promise<CreateIndex> {
  try {
    const credentials = await defaultProvider()();
    const endpoint = new URL(`${domainEndpoint}/${indexName}`);
    const request = new HttpRequest({
      method: 'PUT',
      protocol: 'https:',
      path: `/${indexName}`,
      headers: {
        'host': endpoint.host,
        'content-type': 'application/json',
      },
      body: JSON.stringify({
        settings: {
          number_of_shards: 1,
        },
        mappings: {
          properties: {
            AMAZON_BEDROCK_METADATA: {
              type: 'text',
              fields: {
                keyword: { type: 'keyword' },
              },
            },
            AMAZON_BEDROCK_TEXT_CHUNK: {
              type: 'text',
              fields: {
                keyword: { type: 'keyword' },
              },
            },
            vector_field: {
              type: 'knn_vector',
              dimension: 1024,
              method: {
                name: 'hnsw',
                space_type: 'l2',
                engine: 'faiss',
              },
            },
          },
        },
      }),
    });

    const signer = new SignatureV4({
      credentials,
      region: process.env.S3_STORAGE_REGION || process.env.AWS_REGION || 'us-east-1',
      service: 'aoss',
      sha256: Sha256,
    });

    const signedRequest = await signer.sign(request);
    const response = await axios({
      method: signedRequest.method,
      url: `${domainEndpoint}/${indexName}`,
      headers: signedRequest.headers,
      data: signedRequest.body,
    });
    return response.data;
  } catch (error: any) {
    console.error(`Error creating index "${indexName}": ${error.message}`);
    return {
      acknowledged: false,
      shards_acknowledged: false,
      index: '',
    };
  }
}

async function indexExists(indexName: string): Promise<boolean> {
  try {
    const signedRequest = await getSignedRequest('HEAD', `/${indexName}`);

    const response = await axios({
      method: signedRequest.method,
      url: `${domainEndpoint}/${indexName}`,
      headers: signedRequest.headers,
      validateStatus: () => true, // Không throw lỗi khi status khác 2xx
    });
    console.log(`Index ${indexName} exists: ${response.status}`);
    return response.status === 200;
  } catch (error) {
    console.error(`Error checking index existence: ${error}`);
    return false;
  }
}

async function getAllIndexes(): Promise<string[]> {
  try {
    const signedRequest = await getSignedRequest('GET', '/_aliases');

    const response = await axios({
      method: signedRequest.method,
      url: `${domainEndpoint}/_aliases`,
      headers: signedRequest.headers,
    });

    const indexList = Object.keys(response.data);
    return indexList;
  } catch (error: any) {
    console.error(`Error fetching indexes: ${error.message}`);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
    return [];
  }
}

async function deleteIndex(indexName: string): Promise<any> {
  const signedRequest = await getSignedRequest('DELETE', `/${indexName}`);

  const response = await axios({
    method: signedRequest.method,
    url: `${domainEndpoint}/${indexName}`,
    headers: signedRequest.headers,
  });

  return response.data;
}

async function getIndexByName(indexName: string): Promise<any | null> {
  try {
    const path = `/${indexName}`;
    const signedRequest = await getSignedRequest('GET', path);

    const response = await axios({
      method: signedRequest.method,
      url: `${domainEndpoint}${path}`,
      headers: signedRequest.headers,
    });

    return response.data;
  } catch (error: any) {
    console.error(`Error getting index "${indexName}": ${error.message}`);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
    return null;
  }
}

export { createIndex, getAllIndexes, deleteIndex, indexExists, getIndexByName };
