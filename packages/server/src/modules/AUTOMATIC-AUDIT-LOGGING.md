# Automatic Audit Logging System

## Overview

This system provides **automatic, real-time audit logging** for all database CRUD operations without requiring any code changes to existing endpoints. The system captures all database operations transparently and logs them to an `audit_logs` table.

## ✅ **Zero Code Changes Required**

Your existing API endpoints and database operations continue working exactly as before. The audit logging happens automatically in the background.

## 🚀 **Features**

### **Automatic Integration**
- ✅ **Seamless**: Existing `add()`, `update()`, `remove()`, `get()`, `exists()` functions work unchanged
- ✅ **Transparent**: No modifications needed to existing endpoints
- ✅ **Real-time**: Operations are logged as they happen

### **Context Preservation**
- ✅ **User Information**: Automatically captures username, user_id from requests
- ✅ **Request Details**: IP address, user agent, session ID, request ID
- ✅ **Endpoint Context**: Which API endpoint triggered the operation

### **Performance Optimized**
- ✅ **Asynchronous Logging**: Uses `setImmediate()` to not block main operations
- ✅ **Background Processing**: Logs are queued and processed in batches
- ✅ **Error Isolation**: Audit failures never break main operations

### **Comprehensive Logging**
- ✅ **All CRUD Operations**: CREATE, READ, UPDATE, DELETE
- ✅ **Before/After Values**: Captures data changes for updates
- ✅ **Error Tracking**: Logs failed operations with error messages
- ✅ **Performance Metrics**: Execution time tracking

## 🔧 **Configuration**

### Environment Variables

```bash
# Enable/disable audit logging (default: true)
AUDIT_LOGGING_ENABLED=true

# Log READ operations (default: false - for performance)
AUDIT_LOG_READ_OPERATIONS=false

# Log system operations (default: false)
AUDIT_LOG_SYSTEM_OPERATIONS=false

# Background logging settings
AUDIT_BACKGROUND_LOGGING=true
AUDIT_BATCH_SIZE=10
AUDIT_FLUSH_INTERVAL=1000

# Performance settings
AUDIT_MAX_RETRIES=3
AUDIT_PERFORMANCE_THRESHOLD=1000

# Skip specific tables (comma-separated)
AUDIT_SKIP_TABLES=audit_logs,pg_stat_activity,information_schema

# Skip specific operations (comma-separated)
AUDIT_SKIP_OPERATIONS=

# Debug logging
AUDIT_DEBUG_LOGGING=false
```

## 📊 **What Gets Logged**

### **Automatic Data Capture**

For every database operation, the system automatically captures:

```typescript
{
  action: 'CREATE' | 'READ' | 'UPDATE' | 'DELETE',
  target_type: 'knowledge_base' | 'text_summary' | 'user_token' | 'job' | 'file' | 'folder' | 'agent' | 'document',
  target_id: 'extracted_from_query_or_result',
  target_name: 'human_readable_name',
  username: 'extracted_from_request_headers_or_auth_token',
  user_id: 'extracted_from_request_headers_or_auth_token',
  changes: {
    before: { /* original values */ },
    after: { /* new values */ },
    fields_changed: ['field1', 'field2']
  },
  metadata: {
    request_id: 'unique_request_identifier',
    endpoint: '/api/endpoint',
    method: 'POST',
    table: 'actual_database_table',
    execution_time_ms: 150,
    query_type: 'INSERT',
    is_slow_query: false
  },
  ip_address: '***********',
  user_agent: 'Mozilla/5.0...',
  session_id: 'session_identifier',
  success: true,
  error_message: null
}
```

## 🔄 **How It Works**

### **1. Request Context Capture**
```typescript
// Middleware automatically captures request context
app.use(createAuditMiddleware());
```

### **2. Automatic SQL Analysis**
```typescript
// Every database operation is automatically analyzed
const sqlOperation = SQLAnalyzer.parseSQL(query);
const auditInfo = SQLAnalyzer.extractAuditInfo(sqlOperation, values, result);
```

### **3. Background Logging**
```typescript
// Logging happens asynchronously without blocking
setImmediate(() => {
  logOperation(query, values, result, error, executionTime);
});
```

## 📋 **Examples of Automatic Logging**

### **CREATE Operation**
```typescript
// Your existing code (unchanged):
const result = await add(
  'INSERT INTO knowledge_base (knowledge_base_id, folder_name) VALUES ($1, $2)',
  ['kb123', 'documents']
);

// Automatically logged as:
{
  action: 'CREATE',
  target_type: 'knowledge_base',
  target_id: 'kb123',
  target_name: 'Knowledge Base kb123',
  changes: {
    after: { knowledge_base_id: 'kb123', folder_name: 'documents' },
    fields_changed: ['knowledge_base_id', 'folder_name']
  }
}
```

### **UPDATE Operation**
```typescript
// Your existing code (unchanged):
const result = await update(
  'UPDATE text_summary SET text_summary = $1 WHERE id = $2',
  ['new summary', '456']
);

// Automatically logged as:
{
  action: 'UPDATE',
  target_type: 'text_summary',
  target_id: '456',
  changes: {
    before: { id: '456', text_summary: 'old summary', status: 'draft' },
    after: { id: '456', text_summary: 'new summary', status: 'draft' },
    fields_changed: ['text_summary']
  }
}
```

### **DELETE Operation**
```typescript
// Your existing code (unchanged):
const result = await remove(
  'DELETE FROM user_tokens WHERE user_id = $1',
  ['user123']
);

// Automatically logged as:
{
  action: 'DELETE',
  target_type: 'user_token',
  target_id: 'user123',
  changes: {
    before: { user_id: 'user123', refresh_token: '...', type: 'OneDrive' },
    fields_changed: ['user_id', 'refresh_token', 'type']
  }
}
```

## 🔍 **User Context Detection**

The system automatically extracts user information from:

### **1. Request Headers**
```typescript
// Automatically detected:
x-username: admin
x-user-id: user123
x-session-id: session456
```

### **2. Authorization Tokens**
```typescript
// JWT tokens are automatically parsed for:
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...

// Extracts: username, user_id, email, sub
```

### **3. Fallback Values**
```typescript
// If no user info found:
username: 'anonymous'
user_id: undefined
```

## 📈 **Performance Impact**

### **Minimal Performance Impact**
- ✅ **Asynchronous**: Logging doesn't block main operations
- ✅ **Batched**: Multiple logs processed together
- ✅ **Configurable**: Can disable READ operations logging
- ✅ **Error Isolated**: Audit failures don't affect main operations

### **Performance Metrics**
- ✅ **Execution Time**: Tracks how long each query takes
- ✅ **Slow Query Detection**: Flags queries above threshold
- ✅ **Queue Monitoring**: Background queue size tracking

## 🛠 **Monitoring & Debugging**

### **Debug Logging**
```bash
# Enable debug logging
AUDIT_DEBUG_LOGGING=true

# View debug output
[AUDIT] Logged CREATE operation on knowledge_base { target_id: 'kb123', execution_time: 45, success: true }
[AUDIT] Skipping READ operation logging (disabled in config)
[AUDIT] No audit context available, skipping logging
```

### **Queue Status**
```typescript
import { backgroundLogger } from './background-logger';

// Check queue status
const stats = backgroundLogger.getStats();
console.log(`Queue size: ${stats.queueSize}, Processing: ${stats.isProcessing}`);
```

## 🔒 **Security & Privacy**

### **Sensitive Data Handling**
- ✅ **Password Fields**: Automatically excluded from logging
- ✅ **Token Values**: Only metadata logged, not actual tokens
- ✅ **Configurable Exclusions**: Skip specific tables/operations

### **Data Retention**
- ✅ **Configurable**: Set up automatic cleanup of old audit logs
- ✅ **Archiving**: Move old logs to archive tables
- ✅ **Compliance**: Meets audit trail requirements

## 🚨 **Error Handling**

### **Resilient Design**
- ✅ **Never Breaks Main Operations**: Audit failures are isolated
- ✅ **Retry Logic**: Failed logs are retried with exponential backoff
- ✅ **Graceful Degradation**: System works even if audit table is unavailable

### **Error Scenarios**
```typescript
// Main operation succeeds even if audit fails
try {
  const result = await add(query, values); // ✅ This always works
  return result;
} catch (mainError) {
  throw mainError; // Only main operation errors are thrown
}
// Audit errors are logged but don't propagate
```

## 📊 **Querying Audit Logs**

### **API Endpoints**
```typescript
// Get audit logs with filters
GET /api/audit-logs?action=CREATE&target_type=knowledge_base&username=admin

// Get user activity
GET /api/audit-logs/user/admin?days=30

// Get object history
GET /api/audit-logs/target/knowledge_base/kb123

// Get failed operations
GET /api/audit-logs/failed

// Get statistics
GET /api/audit-logs/stats?days=7
```

## 🎯 **Benefits**

### **For Developers**
- ✅ **Zero Code Changes**: Existing code works unchanged
- ✅ **Automatic**: No manual logging calls needed
- ✅ **Comprehensive**: All operations logged consistently

### **For Operations**
- ✅ **Full Audit Trail**: Complete history of all changes
- ✅ **Performance Monitoring**: Query execution times
- ✅ **Error Tracking**: Failed operations with details

### **For Compliance**
- ✅ **Regulatory Requirements**: Meets audit trail standards
- ✅ **Data Lineage**: Track data changes over time
- ✅ **User Accountability**: Who did what when

## 🔧 **Customization**

### **Adding Custom Target Types**
```typescript
// In sql-analyzer.ts
const TABLE_TO_TARGET_TYPE_MAP: Record<string, AuditTargetType> = {
  'your_table': 'your_target_type',
  // ...
};
```

### **Custom User Extraction**
```typescript
// In audit-context.ts
function extractUsernameFromRequest(req: any): string {
  // Add your custom logic here
  return req.customUserField || 'anonymous';
}
```

This automatic audit logging system provides comprehensive, real-time tracking of all database operations with zero impact on your existing codebase!
