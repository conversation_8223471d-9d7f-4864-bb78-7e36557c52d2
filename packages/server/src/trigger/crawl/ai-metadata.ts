import { logger, task } from '@trigger.dev/sdk/v3';
import axios from 'axios';
import { createHash } from 'crypto';
import { forEach, random } from 'lodash';
import OpenAI from 'openai';
import sharp from 'sharp';

import redis from '../../modules/redis.ts';
import { createFolder, uploadFile, uploadToS3 } from '../../modules/s3.ts';

export type CrawlPayloadDataId = {
  dataId: string;
};

export type CrawlPayloadFull = {
  url: string;
  html: string;
  markdown: string;
  prefix: string;
  title: string;
  crawlId: string;
};

export type CrawlPayload = CrawlPayloadDataId | CrawlPayloadFull;

const client = new OpenAI({
  apiKey: process.env.CRAWL_OPENAI_API_KEY || 'sk-CMC',
  baseURL: process.env.CRAWL_OPENAI_BASE_URL || 'https://litellm.cmcts1.studio.ai.vn/v1',
});

const MAX_RETRIES = 5;
const RETRY_DELAY = 5000;

async function withRetry<T>(
  operation: (attempt?: any) => Promise<T>,
  maxRetries = MAX_RETRIES,
): Promise<T> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation(attempt);
    } catch (error) {
      if (attempt === maxRetries) {
        throw error; // If we've exhausted all retries, throw the error
      }
      logger.warn(
        `Attempt ${attempt} failed, retrying in ${(RETRY_DELAY / 1000).toFixed(2)} seconds...`,
        {
          error,
        },
      );
      await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY));
      // await wait.for({ seconds: RETRY_DELAY / 1000 });
    }
  }
  throw new Error('Should never reach here');
}

export const aiMetadata = task({
  id: 'crawl/ai-metadata',
  machine: {
    preset: 'micro',
  },
  maxDuration: 3600,
  run: exportMetadata,
});

export async function exportMetadata(payload: CrawlPayload) {
  if ('dataId' in payload) {
    try {
      const key = `crawl:data:${payload.dataId}`;
      const fullPayload = JSON.parse((await redis.get(key)) as string);

      payload = fullPayload as CrawlPayloadFull;

      await redis.del(key);

      logger.log('Loaded payload from redis');
    } catch (e: any) {
      logger.warn('load payload error:', e);
      return {};
    }
  }

  payload = payload as CrawlPayloadFull;

  const imageUrls: string[] = [];
  const pdfUrls: string[] = [];

  if (process.env.SHOULD_CRAWL_IMAGE === 'true') {
    // Extract image URLs and their positions from markdown using regex
    const imageRegex = /!\[.*?]\((.*?)\)/g;
    let match;
    while ((match = imageRegex.exec(payload.markdown)) !== null) {
      if (match[1] && !imageUrls.includes(match[1])) {
        imageUrls.push(match[1].split(' ')[0]);
      }
    }
  }

  let match;
  // Extract PDF URLs from HTML
  const pdfRegex = /href=["']([^"']+\.pdf)["']/gi;
  while ((match = pdfRegex.exec(payload.html)) !== null) {
    if (match[1] && !pdfUrls.includes(match[1])) {
      pdfUrls.push(match[1]);
    }
  }

  const imagesBase64: string[] = [];
  const pdfBase64: string[] = [];

  logger.log('SHOULD_CRAWL_IMAGE:', { value: process.env.SHOULD_CRAWL_IMAGE });

  // Process all images in parallel
  if (imageUrls.length) {
    for (let i = 0; i < imageUrls.length; i++) {
      const base64 = await downloadAndProcessImage(imageUrls[i]);
      if (base64) {
        imagesBase64.push(base64);
      }
    }
  }

  // Process all pdf files in parallel
  if (pdfUrls.length) {
    logger.log('Processing PDF files:', { count: pdfUrls.length });
    for (let i = 0; i < pdfUrls.length; i++) {
      logger.log('Processing PDF file:', { index: i, url: pdfUrls[i] });
      const processedPdf = await downloadAndProcessPDF(pdfUrls[i]);
      if (processedPdf && processedPdf.pdfUrl) {
        pdfBase64.push(processedPdf.pdfUrl);
        logger.log('PDF processed successfully', { index: i });
      } else {
        logger.log('Failed to process PDF', { index: i, url: pdfUrls[i] });
      }
    }
    logger.log('Finished processing all PDFs', { successCount: pdfBase64.length });
  }

  const infos: string[] = [];
  let updatedMarkdown = payload.markdown;

  try {
    logger.log('Processing image information', { count: imagesBase64.length });
    for (let index = 0; index < imagesBase64.length; index++) {
      logger.log('Processing image info', { index });
      const value = imagesBase64[index];
      try {
        infos[index] =
          ((await getImageInfo(value)) || '')
            .split('<image_info>')
            .pop()
            ?.split('</image_info>')
            .shift() || '';
        logger.log('Image info processed successfully', { index });
      } catch (error: any) {
        logger.log('Failed to process image info', { index, error: error.message });
      }
    }

    logger.log('Finished processing all images', { successCount: infos.filter(Boolean).length });

    imageUrls.forEach((url, i) => {
      updatedMarkdown = updatedMarkdown.replace(url, `\n\n------\r\n${infos[i]}\r\n------\n\n`);
    });
  } catch (e) {
    console.error(e);
  }

  const kvMetadata = await getMetadata(payload);

  forEach(kvMetadata, (value, key) => {
    if (value === '<UNKNOWN>') {
      kvMetadata[key] = '';
    } else if (value) {
      if (/^(category|sub_cate)/i.test(key)) {
        let formatted = value.replace(/[-_–,.]/g, '').replace(/\s+/g, ' ');
        formatted = formatted.toLowerCase().trim();
        formatted = formatted.charAt(0).toUpperCase() + formatted.slice(1);
        kvMetadata[key] = formatted;
      } else {
        kvMetadata[key] = value;
      }
    }
  });

  const aiResult = {
    output: {
      data: {
        metadata: kvMetadata,
        images: {
          urls: imageUrls,
          infos,
        },
        pdfUrls,
        markdown: updatedMarkdown,
      },
    },
  };

  logger.log('LLM extractor:', aiResult);

  // Parse the URL to create the folder structure
  const url = new URL(payload.url);
  // Remove trailing slash before splitting path
  const cleanPath = url.pathname.replace(/\/$/, '');
  const pathParts = cleanPath.split('/').filter(Boolean);

  // Clean up the prefix and ensure no double slashes
  const cleanPrefix = payload.prefix.trim().replace(/^\/+|\/+$/g, '');
  let folderPath = [cleanPrefix, url.hostname, ...pathParts.slice(0, -1)].filter(Boolean).join('/');

  // Determine content filename based on URL pattern
  let contentFileName: string;
  if (payload.title || url.pathname === '/' || url.pathname === '') {
    // For root domain URLs, use metadata title or index.md
    contentFileName = `${payload.title || 'index'}.md`;
  } else {
    // For all other URLs, use the last segment of the path
    const lastSegment = pathParts[pathParts.length - 1] || 'index';
    // Remove file extension if present and add .md
    contentFileName = `${lastSegment.replace(/\.[^/.]+$/, '')}.md`;
  }

  // LLM meta data
  try {
    console.log('LLM nav extractor', folderPath, contentFileName, 'done');
    folderPath = Array.from(
      new Set([
        cleanPrefix,
        url.hostname,
        aiResult.output.data.metadata.category,
        aiResult.output.data.metadata.sub_cate_1,
        aiResult.output.data.metadata.sub_cate_2,
        aiResult.output.data.metadata.sub_cate_3,
        aiResult.output.data.metadata.sub_cate_4,
        aiResult.output.data.metadata.sub_cate_5,
      ]),
    )
      .filter(Boolean)
      .join('/');
  } catch (e: any) {
    console.error(e?.response?.data || e);
  }

  const markdownBuffer = Buffer.from(aiResult.output.data.markdown, 'utf-8');

  // Create the folder structure
  await createFolder(folderPath);
  await uploadFile(folderPath, contentFileName, markdownBuffer, 'text/markdown; charset=utf-8');
  // Save the metadata with the correct naming pattern
  await uploadFile(
    folderPath,
    `${contentFileName}.metadata.json`,
    Buffer.from(
      JSON.stringify(
        {
          title: payload.title || '',
          url: payload.url,
          // language: item.metadata?.language,
          metadataAttributes: aiResult.output.data.metadata,
        },
        null,
        2,
      ),
      'utf-8',
    ),
    'application/json; charset=utf-8',
  );

  const statusKey = `trigger:status:${payload.crawlId}`;
  await redis.incr(statusKey);
  await redis.expire(statusKey, 60 * 60 * 24); // Set 24h TTL

  return aiResult.output;
}

async function getImageInfo(imageBase64: string) {
  // Create MD5 hash of the image base64 string
  const hash = createHash('md5').update(imageBase64).digest('hex');
  const cacheKey = `image-info/${hash}`;

  // Try to get from cache first
  const cachedResult = await redis.get(cacheKey);
  if (cachedResult) {
    logger.log('image info from cache', { cachedResult });
    return cachedResult;
  }

  return withRetry(async () => {
    try {
      const response = await client.chat.completions.create({
        model: process.env.CRAWL_IMAGE_MODEL || 'claude-3-haiku',
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: `
Extract information from this image.  
  
1. If the image contains text information, please extract the text in Markdown format and maintain the structure provided by the image.
    1. If the image provides a table, please extract the OCR in the correct table format using Markdown.
    2. Extract text accurately from images (usually is Vietnamese), not allowed to be summarized, added, or rewritten.
    3. Note that when using Vietnamese, it must always be accented Vietnamese (example: "tiếng Việt Nam có dấu" not "tieng Viet Nam co dau").

2. If the image does not contain text, please Return a detailed description of the image in Vietnamese to help visually impaired people understand the content.
    1. Some weird images can be logos or icons

Extracted content should be stored in the tag pair <image_info>{EXTRACTED_CONTENT}</image_info>.
          `.trim(),
              },
              {
                type: 'image_url',
                image_url: {
                  url: imageBase64,
                },
              },
            ],
          },
        ],
        temperature: 0,
      });

      const result = response.choices?.[0]?.message.content;

      // Cache the result for 24 hours if we got a valid response
      if (result) {
        await redis.set(cacheKey, result, 'EX', 24 * 60 * 60); // 24 hours in seconds
      }

      return result;
    } catch (e) {
      if (JSON.stringify(e).includes('429')) {
        throw e;
      } else {
        logger.warn('(AI) Lỗi xử lý hình ảnh:', { error: e });
      }
    }
  });
}

async function downloadAndProcessImage(imageUrl: string): Promise<string> {
  logger.log('start download image', { imageUrl });

  try {
    // Download image with axios, no HTTPS verification
    const requestIp = [random(1, 255), random(1, 255), random(1, 255), random(1, 255)].join('.');
    const response = await axios.get(imageUrl, {
      responseType: 'arraybuffer',
      timeout: 20000, // 20 second timeout
      httpsAgent: new (require('https').Agent)({ rejectUnauthorized: false }),
      headers: {
        'x-forwarded-for': requestIp,
        //   'x-real-ip': requestIp,
        //   // 'cf-connecting-ip': requestIp,
        //   // 'true-client-ip': requestIp,
        //   // 'x-client-ip': requestIp,
        //   // 'x-cluster-client-ip': requestIp,
        //   // 'x-forwarded': requestIp,
        //   // 'fastly-client-ip': requestIp,
        //   // 'via': `1.1 ${requestIp}`,
      },
    });

    // Process image with Sharp
    let imageBuffer = await sharp(response.data)
      .png() // Convert to PNG
      .resize(1280, null, {
        // Resize width to 1280 if larger, maintain aspect ratio
        width: 1280,
        withoutEnlargement: true, // Don't enlarge if smaller than 1280
        fit: 'inside',
      })
      .toBuffer();

    logger.warn('download done', { imageUrl });

    // Convert to base64
    return `data:image/png;base64,${imageBuffer.toString('base64')}`;
  } catch (error: any) {
    logger.warn('download failed', { imageUrl, error });
    // Don't retry for specific error types
    if (axios.isAxiosError(error) && error.response) {
      // Don't retry for 404s, 400s, or 500s from the target server
      if (
        error.response.status === 404 ||
        error.response.status === 400 ||
        error.response.status >= 500
      ) {
        logger.warn(`Permanent error processing image ${imageUrl}:`, { error });
        return imageUrl;
      }
    }
  }

  return imageUrl;

  // return withRetry(async () => {
  //   return await limitImageDownloadFn(async () => {
  //     const startDownload = Date.now();
  //     try {
  //       logger.log('start download image', { imageUrl });
  //       // Download image with axios, no HTTPS verification
  //       const requestIp = [random(1, 255), random(1, 255), random(1, 255), random(1, 255)].join(
  //         '.',
  //       );
  //       const response = await axios.get(imageUrl, {
  //         responseType: 'arraybuffer',
  //         timeout: 20000, // 20 second timeout
  //         httpsAgent: new (require('https').Agent)({ rejectUnauthorized: false }),
  //         // headers: {
  //         //   'x-forwarded-for': requestIp,
  //         //   'x-real-ip': requestIp,
  //         //   // 'cf-connecting-ip': requestIp,
  //         //   // 'true-client-ip': requestIp,
  //         //   // 'x-client-ip': requestIp,
  //         //   // 'x-cluster-client-ip': requestIp,
  //         //   // 'x-forwarded': requestIp,
  //         //   // 'fastly-client-ip': requestIp,
  //         //   // 'via': `1.1 ${requestIp}`,
  //         // },
  //       });
  //
  //       // Process image with Sharp
  //       let imageBuffer = await sharp(response.data)
  //         .png() // Convert to PNG
  //         .resize(1280, null, {
  //           // Resize width to 1280 if larger, maintain aspect ratio
  //           width: 1280,
  //           withoutEnlargement: true, // Don't enlarge if smaller than 1280
  //           fit: 'inside',
  //         })
  //         .toBuffer();
  //
  //       // Convert to base64
  //       return `data:image/png;base64,${imageBuffer.toString('base64')}`;
  //     } catch (error: any) {
  //       logger.warn('download failed', { imageUrl, error });
  //       // Don't retry for specific error types
  //       if (axios.isAxiosError(error) && error.response) {
  //         // Don't retry for 404s, 400s, or 500s from the target server
  //         if (
  //           error.response.status === 404 ||
  //           error.response.status === 400 ||
  //           error.response.status >= 500
  //         ) {
  //           logger.warn(`Permanent error processing image ${imageUrl}:`, { error });
  //           return imageUrl;
  //         }
  //       }
  //
  //       // For other errors (timeouts, connection issues, etc), throw to trigger retry
  //       // throw error;
  //       return imageUrl;
  //     } finally {
  //       logger.log('download ' + (Date.now() - startDownload) + 'ms', { imageUrl });
  //     }
  //   });
  // }, 3).catch((error) => {
  //   // After all retries fail, log and return empty string
  //   logger.warn(`Failed to process image ${imageUrl} after all retries:`, { error });
  //   return '';
  // });
}

async function downloadAndProcessPDF(
  pdfUrl: string,
): Promise<{ pdfUrl: string; metadataUrl: string }> {
  return withRetry(async () => {
    const startDownload = Date.now();
    try {
      logger.log('Start download PDF', { pdfUrl });

      const requestIp = [random(1, 255), random(1, 255), random(1, 255)].join('.');
      const response = await axios.get(pdfUrl, {
        responseType: 'arraybuffer',
        timeout: 20000,
        httpsAgent: new (require('https').Agent)({ rejectUnauthorized: false }),
        headers: { 'x-forwarded-for': requestIp, 'x-real-ip': requestIp },
      });

      const originalFileName = pdfUrl.split('/').pop() || 'unknown.pdf';

      const metadata = {
        fileName: originalFileName,
        downloadedAt: new Date().toISOString(),
        sourceUrl: pdfUrl,
      };

      const { pdfUrl: uploadedPdfUrl, metadataUrl } = await uploadToS3(
        originalFileName,
        Buffer.from(response.data),
        metadata,
      );

      logger.log('Uploaded PDF & Metadata to S3', { uploadedPdfUrl, metadataUrl });
      return { pdfUrl: uploadedPdfUrl, metadataUrl };
    } catch (error: any) {
      logger.warn('Download failed', { pdfUrl, error });

      if (axios.isAxiosError(error) && error.response) {
        if (
          error.response.status === 404 ||
          error.response.status === 400 ||
          error.response.status >= 500
        ) {
          logger.warn(`Permanent error processing PDF ${pdfUrl}:`, { error });
          return { pdfUrl, metadataUrl: '' };
        }
      }
      return { pdfUrl, metadataUrl: '' };
    } finally {
      logger.log(`Download took ${Date.now() - startDownload}ms`, { pdfUrl });
    }
  }, 3).catch((error) => {
    logger.warn(`Failed to process PDF ${pdfUrl} after all retries:`, { error });
    return { pdfUrl: '', metadataUrl: '' };
  });
}

async function getMetadata(payload: CrawlPayloadFull) {
  // Estimate 50k tokens as roughly 200,000 characters (assuming ~4 characters per token)
  const MAX_HTML_CHARS = 200000;
  const htmlContent =
    payload.html.length > MAX_HTML_CHARS
      ? payload.html.slice(0, MAX_HTML_CHARS) + '\n\n...[truncated]'
      : payload.html || '<div>error</div>';
  // Create MD5 hash of the image base64 string
  const hash = createHash('md5').update(htmlContent).digest('hex');
  const cacheKey = `html-metadata/${hash}`;

  logger.log('Processing metadata', { url: payload.url, contentLength: payload.html.length, hash });

  logger.log('redis connection:', {
    host: process.env.REDIS_HOST!,
    port: +process.env.REDIS_PORT!,
    password: process.env.REDIS_PASSWORD,
    status: redis.status,
  });

  // Try to get from cache first
  const cachedResult = await redis.get(cacheKey);
  if (cachedResult) {
    try {
      logger.log('Metadata found in cache', { hash });
      return JSON.parse(cachedResult);
    } catch (e) {
      logger.warn('Failed to parse cached metadata:', { error: e });
      // Continue to process if cache parsing fails
    }
  }

  logger.log('Cache miss, fetching metadata from API', { hash });
  return withRetry(async (attempt) => {
    logger.log('Requesting metadata from API', { attempt, url: payload.url });
    const startTime = Date.now();

    const { data: catData } = await axios.post(
      `${process.env.CRAWL_OPENAI_BASE_URL || 'https://litellm.cmcts1.studio.ai.vn/v1'}/chat/completions`,
      {
        stream: false,
        model: process.env.CRAWL_MODEL || 'claude-3.5-haiku',
        temperature: 0,
        messages: [
          {
            role: 'system',
            content: `Extract menu hierarchy from the HTML in this priority order:
1. Navigation menu - Look for main navigation elements like <nav>, menu classes/ids, header navigation, or aside navigation
2. Breadcrumbs - Check for breadcrumb navigation showing the current page hierarchy
3. Aside/Sidebar - Look for hierarchical navigation or category information in <aside> elements, prioritize Aside over Sidebar
4. Content structure - If no explicit navigation, analyze content headings and structure

For each level, identify the currently active/selected item. Pay special attention to <aside> elements as they often contain important category and navigation information.`,
          },
          {
            role: 'user',
            content: (() => {
              return `url: ${payload.url}\n\n\`\`\`html\n${htmlContent}\n\`\`\``;
            })(),
          },
        ],
        response_format: {
          type: 'json_schema',
          json_schema: {
            name: 'menu_extraction',
            description:
              'Extracts hierarchical menu data from HTML by checking navigation, breadcrumbs, and content structure in priority order',
            schema: {
              type: 'object',
              properties: {
                category: {
                  type: 'string',
                  description:
                    'Main navigation category or section that is currently active (Use Vietnamese with tone marks)',
                },
                sub_cate_1: {
                  type: 'string',
                  description:
                    'First level submenu/subsection that is currently active (Use Vietnamese with tone marks)',
                },
                sub_cate_2: {
                  type: 'string',
                  description:
                    'Second level submenu/subsection that is currently active (Use Vietnamese with tone marks)',
                },
                sub_cate_3: {
                  type: 'string',
                  description:
                    'Third level submenu/subsection that is currently active (Use Vietnamese with tone marks)',
                },
                sub_cate_4: {
                  type: 'string',
                  description:
                    'Fourth level submenu/subsection that is currently active (Use Vietnamese with tone marks)',
                },
                sub_cate_5: {
                  type: 'string',
                  description:
                    'Fifth level submenu/subsection that is currently active (Use Vietnamese with tone marks)',
                },
              },
              required: ['category'],
            },
            strict: true,
          },
        },
      },
      {
        headers: {
          Authorization: `Bearer ${process.env.CRAWL_OPENAI_API_KEY || 'sk-CMC'}`,
        },
      },
    );

    const duration = Date.now() - startTime;
    logger.log('API response received', { duration, attempt });

    let llmMetaData = JSON.parse(catData.choices[0].message.content);
    if (typeof llmMetaData.metadataAttributes === 'string') {
      try {
        logger.log('Parsing metadata attributes');
        llmMetaData.metadataAttributes = JSON.parse(llmMetaData.metadataAttributes);
      } catch (error) {
        logger.warn('Failed to parse metadata attributes', { error });
      }
    }

    // Cache the result for 24 hours
    logger.log('Caching metadata results', { hash });
    await redis.set(cacheKey, JSON.stringify(llmMetaData), 'EX', 24 * 60 * 60);

    return llmMetaData;
  });
}
