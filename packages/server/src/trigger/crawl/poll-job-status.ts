import { logger, task, wait } from '@trigger.dev/sdk/v3';
import axios from 'axios';

import { processCompletedJob } from '../../modules/s3.ts';

interface Payload {
  jobUrl: string;
  prefix: string;
}

export const crawlPollJobStatusTask = task({
  id: 'crawl/poll-job-status',
  machine: {
    preset: 'medium-2x',
  },
  // Set an optional maxDuration to prevent tasks from running indefinitely
  maxDuration: 3600 * 24, // Stop executing after 300 secs (5 mins) of compute
  run: async (payload: Payload) => {
    let jobData: any = null;
    let isPolling = true;
    const crawlId: string = payload.jobUrl.split('/').pop()!.split('?')?.shift()!;

    while (isPolling) {
      try {
        let response = await axios.get(payload.jobUrl + '?limit=1');
        jobData = response.data;

        if (jobData.status === 'completed') {
          // Process data page by page to optimize memory usage
          let pageUrl = payload.jobUrl + '?limit=50';
          while (pageUrl) {
            const pageResponse = await axios.get(pageUrl);
            const pageData = pageResponse.data;
            logger.log('Processing page of data', {
              currentPage: pageUrl,
              itemCount: pageData.data.length,
              hasNextPage: !!pageData.next && pageData.next !== pageUrl,
            });
            // Process current page before moving to next
            await processCompletedJob({ ...jobData, data: pageData.data }, payload.prefix, crawlId);
            pageUrl = pageData.next;
          }

          logger.log('Completed processing all pages');

          isPolling = false;
          break;
        } else {
          logger.log('scraping', jobData);
        }

        if (jobData.status === 'failed') {
          console.error('Job failed:', jobData);
          isPolling = false;
          break;
        }

        await wait.for({ seconds: 5 });
      } catch (error) {
        console.error('Error polling job status:', error);
        isPolling = false;
        break;
      }
    }

    return {};
  },
});
