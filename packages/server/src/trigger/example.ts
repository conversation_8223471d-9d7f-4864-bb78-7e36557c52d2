import { logger, task, wait } from '@trigger.dev/sdk/v3';

export const helloWorldTask = task({
  id: 'hello-world',
  machine: {
    preset: 'micro',
  },
  // Set an optional maxDuration to prevent tasks from running indefinitely
  maxDuration: 300, // Stop executing after 300 secs (5 mins) of compute
  run: async (payload: any, { ctx }) => {
    logger.log('Hello, world!', { payload, ctx });

    await wait.for({ seconds: 1 });

    return {
      message: 'Hello, world!',
    };
  },
});
