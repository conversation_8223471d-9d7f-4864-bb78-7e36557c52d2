import { schedules } from '@trigger.dev/sdk/v3';

export const triggerAutomationFlow = schedules.task({
  id: 'cron/trigger-automation-flow',
  cron: '0 * * * *',
  machine: {
    preset: 'small-1x',
  },
  maxDuration: 28800,
  run: async (payload, { ctx }) => {
    try {
      console.log('Run trigger automation flow...');
      await fetch('https://stock.cmcts.ai/c-agent/api/v1/articles/execute/automation-flows', {
        method: 'POST',
      });
    } catch (error) {
      console.error('Error triggerAutomationFlow', error);
    }
  },
});
