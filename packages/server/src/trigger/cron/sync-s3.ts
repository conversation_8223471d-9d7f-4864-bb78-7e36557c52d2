import { schedules } from '@trigger.dev/sdk/v3';

import { startIngestionJob, startSyncKnowledgeBase } from '../../modules/s3';

export const syncS3 = schedules.task({
  id: 'cron/sync-s3',
  // cron: '0 0,8,12 * * *',
  machine: {
    preset: 'small-1x',
  },
  maxDuration: 28800, // Stop executing after 28800 secs (8 hours) of compute
  run: async (payload, { ctx }) => {
    console.log('Syncing Knowledge Base...');
    console.log('Context:', payload.externalId);
    const prefix = payload?.externalId;
    if (!prefix) {
      console.error('No prefix provided in payload:', payload);
      return;
    }
    try {
      const respone = await startIngestionJob(
        prefix,
        '0299b2b0-980a-4d5c-8692-bc69e3b576e2',
        'cmc_ts_admin',
      );
      console.log('Ingestion job response:', respone);
    } catch (error) {
      console.error('Error starting ingestion job in Schedule Job:', error);
    }
  },
});
