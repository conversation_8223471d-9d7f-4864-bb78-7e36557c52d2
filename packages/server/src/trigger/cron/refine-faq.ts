// import { logger, schedules } from '@trigger.dev/sdk/v3';
// import axios from 'axios';
//
// const AUTH_TOKEN =
//   'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjAyOTliMmIwLTk4MGEtNGQ1Yy04NjkyLWJjNjllM2I1NzZlMiIsInVzZXJuYW1lIjoiY21jX3RzX2FkbWluIiwiaWF0IjoxNzQ0MTgwODQ1fQ.sCLpiisWSPCmpu6E4qFQhsliN73k5mpDlA32CX0LJuk';
// const VIB_API_BASE = 'https://vib.cagent.cmcts.ai';
// // const STOCK_API_BASE = 'https://stock.cmcts.ai/c-agent';
// // const DEV_API_BASE = 'https://dev.cagent.cmcts.ai';
//
// const REFINE_FAQ_ENDPOINT = '/api/v1/faq/cronjob/refinefaq';
// // const LIST_FLOWS_ENDPOINT = '/api/v1/faq/listofflowusingfaq';
//
// export const refineFaq = schedules.task({
//   id: 'cron/refine-faq',
//   cron: '* * * * *',
//   machine: {
//     preset: 'small-1x',
//   },
//   maxDuration: 60 * 3,
//   run: async () => {
//     const staticVibChatflowIds = [
//       '217bfde9-d4cb-4b2a-b637-89890a9990ee',
//       '83163854-503c-461f-ba1d-53f304842ad5',
//       '1755164d-cb72-4325-9831-83b295ce42bc',
//     ];
//
//     // let dynamicStockChatflowIds: string[] = [];
//     // try {
//     //   const { data: flowListData } = await axios.get(`${STOCK_API_BASE}${LIST_FLOWS_ENDPOINT}`, {
//     //     headers: {
//     //       Authorization: AUTH_TOKEN,
//     //     },
//     //     timeout: 10000,
//     //   });
//     //   if (Array.isArray(flowListData)) {
//     //     dynamicStockChatflowIds = flowListData.map((item) => item.chatFlow_id).filter((id) => !!id);
//     //   } else {
//     //     logger.warn('Received non-array data from listofflowusingfaq endpoint');
//     //   }
//     // } catch (error: any) {
//     //   logger.error('Failed to fetch dynamic chatflow list:', error);
//     // }
//
//     // let dynamicDevChatflowIds: string[] = [];
//     // try {
//     //   const { data: flowListData } = await axios.get(`${DEV_API_BASE}${LIST_FLOWS_ENDPOINT}`, {
//     //     headers: {
//     //       Authorization: AUTH_TOKEN,
//     //     },
//     //     timeout: 10000,
//     //   });
//     //   if (Array.isArray(flowListData)) {
//     //     dynamicDevChatflowIds = flowListData.map((item) => item.chatFlow_id).filter((id) => !!id);
//     //   } else {
//     //     logger.warn('Received non-array data from listofflowusingfaq endpoint');
//     //   }
//     // } catch (error: any) {
//     //   logger.error('Failed to fetch dynamic chatflow list:', error);
//     // }
//
//     const vibServers = staticVibChatflowIds.map((chatflowId) => ({
//       url: `${VIB_API_BASE}${REFINE_FAQ_ENDPOINT}`,
//       token: AUTH_TOKEN,
//       chatflowId: chatflowId,
//     }));
//
//     // const stockServers = dynamicStockChatflowIds.map((chatflowId) => ({
//     //   url: `${STOCK_API_BASE}${REFINE_FAQ_ENDPOINT}`,
//     //   token: AUTH_TOKEN,
//     //   chatflowId: chatflowId,
//     // }));
//
//     // const devServers = dynamicDevChatflowIds.map((chatflowId) => ({
//     //   url: `${DEV_API_BASE}${REFINE_FAQ_ENDPOINT}`,
//     //   token: AUTH_TOKEN,
//     //   chatflowId: chatflowId,
//     // }));
//
//     // const servers = [...vibServers, ...stockServers, ...devServers];
//     const servers = [...vibServers];
//
//     for (const server of servers) {
//       try {
//         console.log('Starting FAQ refinement process...', server.url);
//
//         axios({
//           method: 'post',
//           url: server.url,
//           headers: {
//             Authorization: server.token,
//           },
//           data: {
//             chatflowId: server.chatflowId,
//             minute: 1.1,
//           },
//           timeout: 10000,
//         });
//
//         logger.log('FAQ refinement completed successfully');
//       } catch (error: any) {
//         logger.error(error);
//       }
//     }
//
//     return {};
//   },
// });
