import { schedules } from '@trigger.dev/sdk/v3';

import {
  getOneDriveAccessToken,
  getRefreshTokenFromDB,
  resyncSyncedItems,
} from '../../modules/oneDrive.ts';

export const syncOneDrive = schedules.task({
  id: 'cron/sync-one-drive',
  cron: '*/1 * * * *',
  maxDuration: 28800, // Stop executing after 28800 secs (8 hours) of compute
  run: async () => {
    console.log('Syncing OneDrive...');
    try {
      const refreshToken = await getRefreshTokenFromDB('1');
      const accessToken = await getOneDriveAccessToken(refreshToken);

      await resyncSyncedItems(accessToken);
      console.log('✅ Hoàn tất resync OneDrive');
    } catch (error) {
      console.error('Error starting ingestion job in Schedule Job:', error);
    }
  },
});
