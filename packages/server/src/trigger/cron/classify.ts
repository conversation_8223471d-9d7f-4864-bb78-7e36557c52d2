// import { logger, schedules } from '@trigger.dev/sdk/v3';
// import axios from 'axios';

// const AUTH_TOKEN =
//   'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjAyOTliMmIwLTk4MGEtNGQ1Yy04NjkyLWJjNjllM2I1NzZlMiIsInVzZXJuYW1lIjoiY21jX3RzX2FkbWluIiwiaWF0IjoxNzQ0MTgwODQ1fQ.sCLpiisWSPCmpu6E4qFQhsliN73k5mpDlA32CX0LJuk';
// // const DEV_API_BASE = 'https://dev.cagent.cmcts.ai';
// const STOCK_API_BASE = 'https://stock.cmcts.ai/c-agent';
// const LIST_FLOWS_ENDPOINT = '/api/v1/faq/listofflowusingfaq';

// export const classify = schedules.task({
//   id: 'cron/classify',
//   cron: '*/20 * * * *',
//   machine: {
//     preset: 'small-1x',
//   },
//   maxDuration: 28800,
//   run: async () => {
//     let stockChatFlowIds: string[] = [];
//     try {
//       const { data: flowListData } = await axios.get(`${STOCK_API_BASE}${LIST_FLOWS_ENDPOINT}`, {
//         headers: {
//           Authorization: AUTH_TOKEN,
//         },
//       });
//       if (Array.isArray(flowListData)) {
//         stockChatFlowIds = flowListData.map((item) => item.chatFlow_id).filter((id) => !!id);
//       } else {
//         logger.warn('Received non-array data from listofflowusingfaq endpoint');
//       }
//     } catch (error: any) {
//       logger.error('Failed to fetch dynamic chatflow list:', error);
//     }

//     for (const flowID of stockChatFlowIds) {
//       await axios({
//         method: 'post',
//         url: `${STOCK_API_BASE}/api/v1/faq/cronjob/classify`,
//         headers: {
//           Authorization: AUTH_TOKEN,
//         },
//         data: {
//           chatflowId: flowID,
//           minute: 20,
//         },
//       });
//     }

//     return {};
//   },
// });
