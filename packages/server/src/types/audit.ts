export type AuditAction = 'CREATE' | 'READ' | 'UPDATE' | 'DELETE';

export type AuditTargetType = 
  | 'agent' 
  | 'folder' 
  | 'file' 
  | 'document' 
  | 'knowledge_base' 
  | 'user_token' 
  | 'text_summary' 
  | 'job';

export type AuditLogEntry = {
  id?: number;
  action: AuditAction;
  target_type: AuditTargetType;
  target_id?: string;
  target_name?: string;
  username: string;
  user_id?: string;
  timestamp?: Date;
  changes?: Record<string, any>;
  metadata?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  session_id?: string;
  error_message?: string;
  success?: boolean;
  created_at?: Date;
  updated_at?: Date;
};

export type AuditLogOptions = {
  username: string;
  user_id?: string;
  ip_address?: string;
  user_agent?: string;
  session_id?: string;
  metadata?: Record<string, any>;
};

export type ChangeSet = {
  before?: Record<string, any>;
  after?: Record<string, any>;
  fields_changed?: string[];
};

export type CreateAuditLogParams = {
  action: AuditAction;
  target_type: AuditTargetType;
  target_id?: string;
  target_name?: string;
  options: AuditLogOptions;
  changes?: ChangeSet;
  error_message?: string;
  success?: boolean;
};

export type AuditLogFilter = {
  action?: AuditAction;
  target_type?: AuditTargetType;
  target_id?: string;
  username?: string;
  user_id?: string;
  start_date?: Date;
  end_date?: Date;
  success?: boolean;
  limit?: number;
  offset?: number;
};

export type AuditLogQueryResult = {
  logs: AuditLogEntry[];
  total_count: number;
  page?: number;
  limit?: number;
  total_pages?: number;
};
