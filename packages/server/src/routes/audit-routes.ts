import { Hono } from 'hono';
import { getAuditLogs } from '../modules/audit-logger';
import type { AuditLogFilter } from '../types/audit';

const auditRoutes = new Hono();

auditRoutes.get('/audit-logs', async (c) => {
  try {
    const {
      action,
      target_type,
      target_id,
      username,
      user_id,
      start_date,
      end_date,
      success,
      page = '1',
      limit = '50',
    } = c.req.query();

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);

    if (isNaN(pageNum) || isNaN(limitNum) || pageNum < 1 || limitNum < 1) {
      return c.json({ error: 'Invalid pagination parameters' }, 400);
    }

    const offset = (pageNum - 1) * limitNum;

    const filter: AuditLogFilter = {
      action: action as any,
      target_type: target_type as any,
      target_id,
      username,
      user_id,
      start_date: start_date ? new Date(start_date) : undefined,
      end_date: end_date ? new Date(end_date) : undefined,
      success: success !== undefined ? success === 'true' : undefined,
      limit: limitNum,
      offset,
    };

    const result = await getAuditLogs(filter);
    return c.json(result);
  } catch (error) {
    console.error('Error fetching audit logs:', error);
    return c.json({ error: 'Failed to fetch audit logs' }, 500);
  }
});

auditRoutes.get('/audit-logs/user/:username', async (c) => {
  try {
    const username = c.req.param('username');
    const { days = '30', limit = '100' } = c.req.query();

    const daysNum = parseInt(days);
    const limitNum = parseInt(limit);

    const start_date = new Date();
    start_date.setDate(start_date.getDate() - daysNum);

    const filter: AuditLogFilter = {
      username,
      start_date,
      limit: limitNum,
      offset: 0,
    };

    const result = await getAuditLogs(filter);
    return c.json(result);
  } catch (error) {
    console.error('Error fetching user audit logs:', error);
    return c.json({ error: 'Failed to fetch user audit logs' }, 500);
  }
});

auditRoutes.get('/audit-logs/target/:target_type/:target_id', async (c) => {
  try {
    const target_type = c.req.param('target_type');
    const target_id = c.req.param('target_id');
    const { limit = '20' } = c.req.query();

    const limitNum = parseInt(limit);

    const filter: AuditLogFilter = {
      target_type: target_type as any,
      target_id,
      limit: limitNum,
      offset: 0,
    };

    const result = await getAuditLogs(filter);
    return c.json(result);
  } catch (error) {
    console.error('Error fetching target audit logs:', error);
    return c.json({ error: 'Failed to fetch target audit logs' }, 500);
  }
});

auditRoutes.get('/audit-logs/failed', async (c) => {
  try {
    const { start_date, end_date, limit = '50' } = c.req.query();

    const limitNum = parseInt(limit);

    const filter: AuditLogFilter = {
      success: false,
      start_date: start_date ? new Date(start_date) : undefined,
      end_date: end_date ? new Date(end_date) : undefined,
      limit: limitNum,
      offset: 0,
    };

    const result = await getAuditLogs(filter);
    return c.json(result);
  } catch (error) {
    console.error('Error fetching failed operations:', error);
    return c.json({ error: 'Failed to fetch failed operations' }, 500);
  }
});

auditRoutes.get('/audit-logs/stats', async (c) => {
  try {
    const { days = '7' } = c.req.query();
    const daysNum = parseInt(days);

    const start_date = new Date();
    start_date.setDate(start_date.getDate() - daysNum);

    const allLogs = await getAuditLogs({
      start_date,
      limit: 10000,
      offset: 0,
    });

    const stats = {
      total_operations: allLogs.total_count,
      successful_operations: allLogs.logs.filter(log => log.success).length,
      failed_operations: allLogs.logs.filter(log => !log.success).length,
      operations_by_action: {} as Record<string, number>,
      operations_by_target_type: {} as Record<string, number>,
      operations_by_user: {} as Record<string, number>,
      daily_activity: {} as Record<string, number>,
    };

    allLogs.logs.forEach(log => {
      stats.operations_by_action[log.action] = (stats.operations_by_action[log.action] || 0) + 1;
      stats.operations_by_target_type[log.target_type] = (stats.operations_by_target_type[log.target_type] || 0) + 1;
      stats.operations_by_user[log.username] = (stats.operations_by_user[log.username] || 0) + 1;

      const date = new Date(log.timestamp!).toISOString().split('T')[0];
      stats.daily_activity[date] = (stats.daily_activity[date] || 0) + 1;
    });

    return c.json(stats);
  } catch (error) {
    console.error('Error generating audit stats:', error);
    return c.json({ error: 'Failed to generate audit statistics' }, 500);
  }
});

export default auditRoutes;
