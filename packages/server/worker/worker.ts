import { Queue, Worker } from 'bullmq';

import { redisConnection } from '../src/modules/redis';
import CleanupJob from './cleanup-job';
import FooJob from './foo-job';

const BATCH_SIZE = +(process.env.BATCH_SIZE || 50);
const JOBS: Record<any, any> = {
  FooJob,
  CleanupJob,
};

export const queueDefault = new Queue('default', {
  connection: redisConnection,
});

let worker: Worker | null = null;

/**
 * Clean up failed tasks on server startup
 */
export async function cleanupFailedTasks() {
  console.log('🧹 Starting cleanup of stalled BullMQ jobs...');

  try {
    // Get all active jobs
    const activeJobs = await queueDefault.getActive();
    console.log(`Found ${activeJobs.length} active BullMQ jobs to clean up`);

    // Move active jobs to failed state
    for (const job of activeJobs) {
      try {
        await job.moveToFailed(new Error('Server restart - job was interrupted'), '0');
        // console.log(`Moved job ${job.id} to failed state`);
      } catch (error) {
        console.error(`Error moving job ${job.id} to failed:`, error);
      }
    }

    // Get all waiting jobs
    const waitingJobs = await queueDefault.getWaiting();
    console.log(`Found ${waitingJobs.length} waiting BullMQ jobs`);

    // Clean up old completed and failed jobs (keep last 100 of each)
    await queueDefault.clean(24 * 60 * 60 * 1000, 100, 'completed'); // 24 hours old
    await queueDefault.clean(24 * 60 * 60 * 1000, 100, 'failed'); // 24 hours old

    console.log('✅ BullMQ cleanup completed');
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  }
}

export default async function setupWorker() {
  // Clean up failed tasks before starting worker
  await cleanupFailedTasks();

  worker = new Worker(
    'default',
    async (job) => {
      let result: any;

      for (const jobPattern of Object.keys(JOBS)) {
        if (job.name.startsWith(jobPattern)) {
          result = await JOBS[jobPattern](job, job.data);
          break;
        }
      }

      return result;
    },
    {
      connection: redisConnection,
      concurrency: BATCH_SIZE,
      skipStalledCheck: false, // Enable stalled check for better reliability
      stalledInterval: 30 * 1000, // Check for stalled jobs every 30 seconds
      maxStalledCount: 1, // Max number of times a job can be stalled before failing
    },
  );

  // Add error handling for the worker
  worker.on('error', (error) => {
    console.error('Worker error:', error);
  });

  worker.on('failed', (job, err) => {
    console.error(`Job ${job?.id} failed:`, err);
  });

  // Set up daily cleanup job at midnight (00:00)
  await setupDailyCleanupJob();
}

// Set up daily cleanup job scheduler that runs at midnight (00:00)
async function setupDailyCleanupJob() {
  try {
    // Remove any existing cleanup job scheduler to avoid duplicates
    await queueDefault.removeJobScheduler('daily-cleanup');

    // Add the job scheduler for daily cleanup
    await queueDefault.upsertJobScheduler(
      'daily-cleanup',
      {
        pattern: '0 0 * * *', // Daily at midnight (00:00)
      },
      {
        name: 'CleanupJob',
        data: {
          description: 'Daily cleanup of old completed and failed jobs',
        },
      },
    );

    console.log('✅ Daily cleanup job scheduler created for midnight (00:00)');
  } catch (error) {
    console.error('❌ Error setting up daily cleanup job scheduler:', error);
  }
}

/**
 * Gracefully shutdown the worker
 */
export async function shutdownWorker() {
  console.log('🛑 Shutting down worker...');

  if (worker) {
    try {
      await worker.close();
      console.log('✅ Worker shutdown completed');
    } catch (error) {
      console.error('❌ Error shutting down worker:', error);
    }
  }
}
