import { Job } from 'bullmq';

import { queueDefault } from './worker';

/**
 * Daily cleanup job that runs the two cleanup commands:
 * 1. Clean up old completed jobs (keep last 100, older than 24 hours)
 * 2. Clean up old failed jobs (keep last 100, older than 24 hours)
 */
export default async function CleanupJob(job: Job, data: Record<any, any>) {
  console.log('🧹 Starting daily BullMQ cleanup job...');

  try {
    // Clean up old completed jobs (keep last 100 of each)
    await queueDefault.clean(24 * 60 * 60 * 1000, 100, 'completed'); // 24 hours old
    console.log('✅ Cleaned up old completed jobs');

    // Clean up old failed jobs (keep last 100 of each)
    await queueDefault.clean(24 * 60 * 60 * 1000, 100, 'failed'); // 24 hours old
    console.log('✅ Cleaned up old failed jobs');

    console.log('✅ Daily BullMQ cleanup completed successfully');

    return {
      success: true,
      message: 'Daily cleanup completed successfully',
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error('❌ Error during daily cleanup:', error);
    throw error;
  }
}
