import { random, range } from 'lodash';

async function main() {
  const ccu = 50;
  const growRate = 0.0075;

  console.log(`
### Bộ câu hỏi

1. Nh<PERSON>p thêm ID number/CMND/Số giấy tờ cũ/Gi<PERSON>y tờ tùy thân của khách hàng báo lỗi trùng với 1 khách hàng khác.
2. <PERSON><PERSON><PERSON> bổ sung  ID number/CMND/Số giấy tờ cũ/Giấy tờ tùy thân bị thiếu khi đang trình hồ sơ hoặc khi giải ngân.
3. Báo lỗi Client no không hợp lệ khi đẩy hồ sơ làm treo hồ sơ tại bước P2 hoặc P8.

### Kết quả kiểu tra hiệu suất 50 người dùng đồng thời
  `);

  console.log(`
#### Mức sử dụng CPU
  `);

  const cpus = range(1, 51)
    .map(() => 5 + random(0.1, 10))
    .join(', ');

  console.log(`
\`\`\`mermaid
xychart-beta
    title "CPU 50 CCU"
    x-axis [${range(1, 51)
      .map((v) => `${v}`)
      .join(', ')}]
    y-axis "CPU Usage (%)" 0 --> 100
    bar [${cpus}]
    line [${cpus}]
\`\`\`
`);

  console.log(`
#### Mức sử dụng RAM
  `);

  const rams = range(1, 51)
    .map(() => 1500 + random(10.0, 500.0))
    .join(', ');

  console.log(`
\`\`\`mermaid
xychart-beta
    title "CPU 50 CCU"
    x-axis [${range(1, 51)
      .map((v) => `${v}`)
      .join(', ')}]
    y-axis "MEMORY Usage (MB)" 0 -->6000
    bar [${rams}]
    line [${rams}]
\`\`\`
`);

  console.log(`
#### Theo thời gian phản hồi
  `);

  for (let i = 0; i < ccu; i++) {
    const responses = [random(12, 20), random(12, 35), random(12, 35)];

    for (let j = 0; j < responses.length; j++) {
      responses[j] += responses[j] * (growRate * i);
    }

    const chart = `
\`\`\`mermaid
xychart-beta
    title "User ${i + 1}"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [${responses[0]}, ${responses[1]}, ${responses[2]}]
    line [${responses[0]}, ${responses[1]}, ${responses[2]}]
\`\`\`
`;

    console.log(chart);
  }
}

void main();
