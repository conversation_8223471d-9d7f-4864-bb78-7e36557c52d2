### <PERSON><PERSON> câu hỏi

1. <PERSON><PERSON><PERSON><PERSON> thêm ID number/CMND/<PERSON>ố giấy tờ cũ/<PERSON><PERSON><PERSON><PERSON> tờ tùy thân của khách hàng báo lỗi trùng với 1 khách hàng khác.
2. <PERSON><PERSON><PERSON> b<PERSON> sung  ID number/CMND/Số giấy tờ cũ/<PERSON><PERSON><PERSON><PERSON> tờ tùy thân bị thiếu khi đang trình hồ sơ hoặc khi giải ngân.
3. Báo lỗi Client no không hợp lệ khi đẩy hồ sơ làm treo hồ sơ tại bước P2 hoặc P8.

### K<PERSON>t quả kiểu tra hiệu suất 50 người dùng đồng thời


#### <PERSON><PERSON><PERSON> sử dụng CPU


```mermaid
xychart-beta
    title "CPU 50 CCU"
    x-axis [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50]
    y-axis "CPU Usage (%)" 0 --> 100
    bar [5.673812945500999, 7.066126859845111, 9.458322529416545, 10.756299300237973, 13.689346631310089, 7.894971582315593, 7.089896235247566, 13.310114042252287, 11.49054457775433, 13.829616061654873, 8.857986980198202, 10.26865292108457, 12.867967678953793, 12.870384362939873, 13.474899448227772, 7.264483656798933, 11.820818244541162, 11.74512320336039, 13.720846621354033, 9.426101867019188, 5.345771287485134, 12.793367344322057, 9.056736390898159, 8.692946514356603, 11.728727045257298, 10.580506488367181, 11.460723751346187, 7.636269215746015, 6.079011231400285, 8.491752413536311, 11.800944839523227, 7.280109085452045, 6.4522128171900945, 6.405022922371329, 8.549905406174657, 10.923334146269026, 13.730570840933042, 7.095358333229318, 7.81896521873518, 13.359692965636386, 11.160607268257372, 11.222761469924745, 10.585945036754005, 8.732095965624435, 8.284190837565385, 6.740711817799627, 7.812819447840486, 9.784181073662527, 12.196498358013441, 10.221208382852366]
    line [5.673812945500999, 7.066126859845111, 9.458322529416545, 10.756299300237973, 13.689346631310089, 7.894971582315593, 7.089896235247566, 13.310114042252287, 11.49054457775433, 13.829616061654873, 8.857986980198202, 10.26865292108457, 12.867967678953793, 12.870384362939873, 13.474899448227772, 7.264483656798933, 11.820818244541162, 11.74512320336039, 13.720846621354033, 9.426101867019188, 5.345771287485134, 12.793367344322057, 9.056736390898159, 8.692946514356603, 11.728727045257298, 10.580506488367181, 11.460723751346187, 7.636269215746015, 6.079011231400285, 8.491752413536311, 11.800944839523227, 7.280109085452045, 6.4522128171900945, 6.405022922371329, 8.549905406174657, 10.923334146269026, 13.730570840933042, 7.095358333229318, 7.81896521873518, 13.359692965636386, 11.160607268257372, 11.222761469924745, 10.585945036754005, 8.732095965624435, 8.284190837565385, 6.740711817799627, 7.812819447840486, 9.784181073662527, 12.196498358013441, 10.221208382852366]
```


#### Mức sử dụng RAM


```mermaid
xychart-beta
    title "CPU 50 CCU"
    x-axis [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50]
    y-axis "MEMORY Usage (MB)" 0 -->6000
    bar [1870, 1997, 1817, 1745, 1838, 1516, 1996, 1811, 1968, 1832, 1864, 1784, 1986, 1770, 1523, 1780, 1672, 1670, 1739, 1898, 1783, 1995, 1846, 1985, 1623, 1604, 1737, 1687, 1520, 1521, 1794, 1929, 1775, 1818, 1850, 1884, 1655, 1765, 1761, 1575, 1660, 1716, 1943, 1674, 1759, 1600, 1658, 1990, 1696, 1561]
    line [1870, 1997, 1817, 1745, 1838, 1516, 1996, 1811, 1968, 1832, 1864, 1784, 1986, 1770, 1523, 1780, 1672, 1670, 1739, 1898, 1783, 1995, 1846, 1985, 1623, 1604, 1737, 1687, 1520, 1521, 1794, 1929, 1775, 1818, 1850, 1884, 1655, 1765, 1761, 1575, 1660, 1716, 1943, 1674, 1759, 1600, 1658, 1990, 1696, 1561]
```


#### Theo thời gian phản hồi


```mermaid
xychart-beta
    title "User 1"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [18, 13, 18]
    line [18, 13, 18]
```


```mermaid
xychart-beta
    title "User 2"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [16.12, 13.0975, 14.105]
    line [16.12, 13.0975, 14.105]
```


```mermaid
xychart-beta
    title "User 3"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [13.195, 22.33, 19.285]
    line [13.195, 22.33, 19.285]
```


```mermaid
xychart-beta
    title "User 4"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [14.315, 30.675, 26.585]
    line [14.315, 30.675, 26.585]
```


```mermaid
xychart-beta
    title "User 5"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [19.57, 12.36, 28.84]
    line [19.57, 12.36, 28.84]
```


```mermaid
xychart-beta
    title "User 6"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [16.6, 18.675, 17.6375]
    line [16.6, 18.675, 17.6375]
```


```mermaid
xychart-beta
    title "User 7"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [15.675, 21.945, 27.17]
    line [15.675, 21.945, 27.17]
```


```mermaid
xychart-beta
    title "User 8"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [21.05, 12.63, 12.63]
    line [21.05, 12.63, 12.63]
```


```mermaid
xychart-beta
    title "User 9"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [19.08, 18.02, 30.74]
    line [19.08, 18.02, 30.74]
```


```mermaid
xychart-beta
    title "User 10"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [12.81, 30.9575, 32.025]
    line [12.81, 30.9575, 32.025]
```


```mermaid
xychart-beta
    title "User 11"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [17.2, 27.95, 31.175]
    line [17.2, 27.95, 31.175]
```


```mermaid
xychart-beta
    title "User 12"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [16.2375, 12.99, 32.475]
    line [16.2375, 12.99, 32.475]
```


```mermaid
xychart-beta
    title "User 13"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [15.26, 37.06, 30.52]
    line [15.26, 37.06, 30.52]
```


```mermaid
xychart-beta
    title "User 14"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [16.4625, 15.365, 26.34]
    line [16.4625, 15.365, 26.34]
```


```mermaid
xychart-beta
    title "User 15"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [18.785, 30.94, 15.47]
    line [18.785, 30.94, 15.47]
```


```mermaid
xychart-beta
    title "User 16"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [13.35, 20.025, 16.6875]
    line [13.35, 20.025, 16.6875]
```


```mermaid
xychart-beta
    title "User 17"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [20.16, 23.52, 32.48]
    line [20.16, 23.52, 32.48]
```


```mermaid
xychart-beta
    title "User 18"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [21.4225, 33.825, 15.785]
    line [21.4225, 33.825, 15.785]
```


```mermaid
xychart-beta
    title "User 19"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [20.43, 37.455, 19.295]
    line [20.43, 37.455, 19.295]
```


```mermaid
xychart-beta
    title "User 20"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [21.7075, 30.8475, 33.1325]
    line [21.7075, 30.8475, 33.1325]
```


```mermaid
xychart-beta
    title "User 21"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [17.25, 14.95, 40.25]
    line [17.25, 14.95, 40.25]
```


```mermaid
xychart-beta
    title "User 22"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [21.9925, 40.5125, 33.5675]
    line [21.9925, 40.5125, 33.5675]
```


```mermaid
xychart-beta
    title "User 23"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [15.145, 40.775, 29.125]
    line [15.145, 40.775, 29.125]
```


```mermaid
xychart-beta
    title "User 24"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [23.45, 23.45, 18.759999999999998]
    line [23.45, 23.45, 18.759999999999998]
```


```mermaid
xychart-beta
    title "User 25"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [20.06, 38.94, 21.24]
    line [20.06, 38.94, 21.24]
```


```mermaid
xychart-beta
    title "User 26"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [16.625, 17.8125, 29.6875]
    line [16.625, 17.8125, 29.6875]
```


```mermaid
xychart-beta
    title "User 27"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [23.9, 17.925, 21.51]
    line [23.9, 17.925, 21.51]
```


```mermaid
xychart-beta
    title "User 28"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [14.43, 21.645, 37.2775]
    line [14.43, 21.645, 37.2775]
```


```mermaid
xychart-beta
    title "User 29"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [21.78, 18.15, 22.99]
    line [21.78, 18.15, 22.99]
```


```mermaid
xychart-beta
    title "User 30"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [17.045, 17.045, 42.6125]
    line [17.045, 17.045, 42.6125]
```


```mermaid
xychart-beta
    title "User 31"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [23.275, 39.2, 20.825]
    line [23.275, 39.2, 20.825]
```


```mermaid
xychart-beta
    title "User 32"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [20.9525, 30.8125, 39.44]
    line [20.9525, 30.8125, 39.44]
```


```mermaid
xychart-beta
    title "User 33"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [23.56, 43.4, 22.32]
    line [23.56, 43.4, 22.32]
```


```mermaid
xychart-beta
    title "User 34"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [19.96, 37.425, 22.455]
    line [19.96, 37.425, 22.455]
```


```mermaid
xychart-beta
    title "User 35"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [17.57, 15.06, 41.415]
    line [17.57, 15.06, 41.415]
```


```mermaid
xychart-beta
    title "User 36"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [17.675, 23.9875, 41.6625]
    line [17.675, 23.9875, 41.6625]
```


```mermaid
xychart-beta
    title "User 37"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [17.78, 29.21, 39.370000000000005]
    line [17.78, 29.21, 39.370000000000005]
```


```mermaid
xychart-beta
    title "User 38"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [21.7175, 26.8275, 33.214999999999996]
    line [21.7175, 26.8275, 33.214999999999996]
```


```mermaid
xychart-beta
    title "User 39"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [19.275, 38.55, 24.415]
    line [19.275, 38.55, 24.415]
```


```mermaid
xychart-beta
    title "User 40"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [24.557499999999997, 40.067499999999995, 31.02]
    line [24.557499999999997, 40.067499999999995, 31.02]
```


```mermaid
xychart-beta
    title "User 41"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [23.4, 28.6, 41.6]
    line [23.4, 28.6, 41.6]
```


```mermaid
xychart-beta
    title "User 42"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [16.9975, 33.995, 44.455]
    line [16.9975, 33.995, 44.455]
```


```mermaid
xychart-beta
    title "User 43"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [15.780000000000001, 30.245, 18.41]
    line [15.780000000000001, 30.245, 18.41]
```


```mermaid
xychart-beta
    title "User 44"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [26.45, 30.4175, 33.0625]
    line [26.45, 30.4175, 33.0625]
```


```mermaid
xychart-beta
    title "User 45"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [25.27, 17.29, 37.239999999999995]
    line [25.27, 17.29, 37.239999999999995]
```


```mermaid
xychart-beta
    title "User 46"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [16.05, 41.4625, 21.4]
    line [16.05, 41.4625, 21.4]
```


```mermaid
xychart-beta
    title "User 47"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [26.9, 37.66, 20.175]
    line [26.9, 37.66, 20.175]
```


```mermaid
xychart-beta
    title "User 48"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [24.345, 41.9275, 22.9925]
    line [24.345, 41.9275, 22.9925]
```


```mermaid
xychart-beta
    title "User 49"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [19.04, 42.16, 47.6]
    line [19.04, 42.16, 47.6]
```


```mermaid
xychart-beta
    title "User 50"
    x-axis [question1, question2, question3]
    y-axis "Response time (s)" 0 --> 60
    bar [20.5125, 39.6575, 43.76]
    line [20.5125, 39.6575, 43.76]
```



