import axios from 'axios';
import { v4 } from 'uuid';

const CCU = 10;
// const DEFAULT_FLOW_ID = '7a3405d1-2c6a-435e-9793-2d570577c69a';
const DEFAULT_FLOW_ID = '217bfde9-d4cb-4b2a-b637-89890a9990ee';

async function query(flowId: string = DEFAULT_FLOW_ID, data: any) {
  const startAt = performance.now();

  const response = await axios({
    method: 'POST',
    url: `https://vib.cagent.cmcts.ai/api/v1/prediction/${flowId}`,
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
    responseType: 'stream',
  });

  // Handle streaming response
  let result = '';

  return new Promise((resolve, reject) => {
    response.data.on('data', (chunk: Buffer) => {
      const chunkStr = chunk.toString();

      const events = chunkStr
        .split('message:\ndata:')
        .filter((v) => v.includes('event'))
        .map((v) => {
          try {
            return JSON.parse(v);
          } catch (e) {
            return null;
          }
        })
        .filter(Boolean);

      let shouldResolve = false;

      for (const event of events) {
        // console.log('event', event);
        if (event.event === 'token') result += event.data;
        if (event.event === 'sourceDocuments') shouldResolve = true;
      }

      if (shouldResolve) {
        resolve({ time: performance.now() - startAt, result });
      }
    });

    response.data.on('error', (err: Error) => {
      reject(err);
    });

    response.data.on('end', () => {
      resolve({ time: performance.now() - startAt, result });
    });
  });
}

async function simulateChat(index: number) {
  const chatHistories = [
    'Nhập thêm ID number/CMND/Số giấy tờ cũ/Giấy tờ tùy thân của khách hàng báo lỗi trùng với 1 khách hàng khác',
    'Cần bổ sung  ID number/CMND/Số giấy tờ cũ/Giấy tờ tùy thân bị thiếu khi đang trình hồ sơ hoặc khi giải ngân.',
    'Báo lỗi Client no không hợp lệ khi đẩy hồ sơ làm treo hồ sơ tại bước P2 hoặc P8',
  ];
  const conversationId = v4();
  const botId = v4();
  const startTime = performance.now();

  console.log('starting chat: User ', index + 1);

  for (let i = 0; i < chatHistories.length; i++) {
    const result: any = await query(DEFAULT_FLOW_ID, {
      question: chatHistories[i],
      chatId: conversationId,
      overrideConfig: {},
      stored: {
        deviceId: botId,
        chatwoot: {
          conversation_id: null,
        },
        userInfo: {},
      },
      streaming: true,
    });

    console.log(`User: ${index + 1} - Question ${i + 1}:`, (result.time / 1000.0).toFixed(2) + 's');
  }

  console.log(
    `User: ${index + 1} - End:`,
    index,
    ((performance.now() - startTime) / 1000.0).toFixed(2),
  );
}

async function main() {
  for (let i = 0; i < CCU; i++) {
    await simulateChat(i);
  }
}

void main();
