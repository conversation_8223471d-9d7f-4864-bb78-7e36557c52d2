-- Create audit_logs table for tracking CRUD operations
CREATE TABLE IF NOT EXISTS audit_logs (
    id SERIAL PRIMARY KEY,
    action VARCHAR(10) NOT NULL CHECK (action IN ('CREATE', 'READ', 'UPDATE', 'DELETE')),
    target_type VARCHAR(20) NOT NULL CHECK (target_type IN ('agent', 'folder', 'file', 'document', 'knowledge_base', 'user_token', 'text_summary', 'job')),
    target_id VARCHAR(255),
    target_name VARCHAR(500),
    username VARCHAR(255) NOT NULL,
    user_id VARCHAR(255),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    changes JSONB,
    metadata JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    error_message TEXT,
    success BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_target_type ON audit_logs(target_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_target_id ON audit_logs(target_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_username ON audit_logs(username);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_logs_success ON audit_logs(success);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);

-- Create GIN index for JSONB columns for efficient querying
CREATE INDEX IF NOT EXISTS idx_audit_logs_changes_gin ON audit_logs USING GIN(changes);
CREATE INDEX IF NOT EXISTS idx_audit_logs_metadata_gin ON audit_logs USING GIN(metadata);

-- Add comments for documentation
COMMENT ON TABLE audit_logs IS 'Audit trail for all CRUD operations in the system';
COMMENT ON COLUMN audit_logs.action IS 'Type of operation: CREATE, READ, UPDATE, DELETE';
COMMENT ON COLUMN audit_logs.target_type IS 'Type of entity being operated on';
COMMENT ON COLUMN audit_logs.target_id IS 'Unique identifier of the target entity';
COMMENT ON COLUMN audit_logs.target_name IS 'Human-readable name of the target entity';
COMMENT ON COLUMN audit_logs.username IS 'Username of the user performing the action';
COMMENT ON COLUMN audit_logs.user_id IS 'Unique identifier of the user performing the action';
COMMENT ON COLUMN audit_logs.changes IS 'JSONB object containing before/after values for updates';
COMMENT ON COLUMN audit_logs.metadata IS 'Additional context information about the operation';
COMMENT ON COLUMN audit_logs.ip_address IS 'IP address of the client making the request';
COMMENT ON COLUMN audit_logs.user_agent IS 'User agent string of the client';
COMMENT ON COLUMN audit_logs.session_id IS 'Session identifier for tracking user sessions';
COMMENT ON COLUMN audit_logs.error_message IS 'Error message if the operation failed';
COMMENT ON COLUMN audit_logs.success IS 'Whether the operation was successful';
