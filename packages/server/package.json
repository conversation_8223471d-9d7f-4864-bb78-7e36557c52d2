{"name": "server", "type": "module", "module": "index.ts", "scripts": {"build": "bun build index.ts --outdir out --target bun", "dev": "bun --watch run index.ts", "start": "bun run out/index.js", "trigger:deploy": "npx trigger.dev@latest deploy --self-hosted --push", "trigger:dev": "npx trigger.dev@latest dev", "trigger:init": "npx trigger.dev@latest init -p proj_caxciysgvptlhamdzwmi -a https://trigger.1882.studio.ai.vn --override-config", "trigger:login": "npx trigger.dev login -a https://trigger.1882.studio.ai.vn", "trigger:logout": "npx trigger.dev logout --profile default -a https://trigger.1882.studio.ai.vn"}, "dependencies": {"@aws-crypto/sha256-js": "^5.2.0", "@aws-sdk/client-bedrock-agent": "^3.726.1", "@aws-sdk/client-bedrock-runtime": "3.422.0", "@aws-sdk/client-s3": "3.709.0", "@aws-sdk/credential-provider-node": "^3.821.0", "@aws-sdk/protocol-http": "^3.374.0", "@aws-sdk/signature-v4": "^3.374.0", "@azure/msal-browser": "^4.12.0", "@bull-board/api": "^6.9.6", "@bull-board/hono": "^6.9.6", "@hono/node-server": "^1.14.3", "@trigger.dev/sdk": "^3.3.17", "@types/mime-types": "^2.1.4", "archiver": "^7.0.1", "axios": "^1.7.9", "axios-retry": "^4.5.0", "bullmq": "^5.53.0", "dotenv": "^16.4.7", "googleapis": "^146.0.0", "hono": "^4.6.13", "ioredis": "^5.4.1", "lodash": "^4.17.21", "meilisearch": "^0.49.0", "mime-types": "^2.1.35", "openai": "^4.83.0", "p-limit": "^6.2.0", "pg": "^8.11.10", "sharp": "^0.33.5", "uuid": "^11.1.0"}, "devDependencies": {"@trigger.dev/build": "^3.3.17", "@types/archiver": "^6.0.3", "@types/axios": "^0.14.4", "@types/bun": "latest", "@types/ioredis": "^5.0.0", "@types/lodash": "^4.17.15", "@types/pg": "^8.11.10"}, "peerDependencies": {"typescript": "^5.0.0"}}